import React, {useEffect, useState, useRef} from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  CircularProgress,
  Divider,
  ListItem,
  IconButton,
  Tooltip,
  Chip,
  useTheme,
  useMediaQuery,
  Stack,
} from "@mui/material";
import {
  Close as CloseIcon,
  Reorder as ReorderIcon,
  Save as SaveIcon,
  Autorenew as RebalanceIcon,
  DragIndicator as DragHandleIcon,
} from "@mui/icons-material";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { ButtonBasic, ButtonBasicCancel, SubmitButtons } from "../../components/buttons/Buttons";

const ArrangeStepsDialog = ({
  open,
  onClose,
  currentMode,
  maintenanceId,
  useAt,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [steps, setSteps] = useState([]);
  const [loadingSteps, setLoadingSteps] = useState(false);
  const [dirty, setDirty] = useState(false);
  const originalStepsRef = useRef([]);

  const isDarkMode = currentMode === "Dark";

  useEffect(() => {
    if (open && maintenanceId) {
      checkAndRebalance();
    }
    // eslint-disable-next-line
  }, [open, maintenanceId]);

  const checkAndRebalance = async () => {
    setLoadingSteps(true);
    try {
      const fullUrl =
        useAt === "Alarm SOP"
          ? `${dbConfig.url}/alarmSopStepData/getAlarmSOPSteps/${maintenanceId}`
          : `${dbConfig.url}/stepdata/getManualSteps/${maintenanceId}`;
      const response = await axios.get(fullUrl);

      const stepsData = response?.data?.data || [];

      const missingSortKey = stepsData.some(
        step => step.sortKey === undefined || step.sortKey === null,
      );

      const negativeSortKey = stepsData.some(
        step => typeof step.sortKey === "number" && step.sortKey < 0,
      );

      const sortKeys = stepsData
        .map(step => step.sortKey)
        .filter(key => typeof key === "number")
        .sort((a, b) => a - b);

      let tooClose = false;
      for (let i = 1; i < sortKeys.length; i++) {
        if (sortKeys[i] - sortKeys[i - 1] < 2) {
          tooClose = true;
          break;
        }
      }

      const needsRebalance = missingSortKey || negativeSortKey || tooClose;

      if (needsRebalance) {
        const fullUrl =
          useAt !== "Alarm SOP"
            ? `${dbConfig.url}/stepdata/rebalance/${maintenanceId}`
            : `${dbConfig.url}/alarmSopStepData/rebalance/${maintenanceId}`;
        await axios.put(fullUrl);
        toastMessageSuccess({message: "Steps rebalanced successfully."});
      }

      await getAllSteps();
    } catch (error) {
      toastMessage({
        message: "Failed to load or rebalance steps",
        type: "error",
      });
      setSteps([]);
      originalStepsRef.current = [];
      setDirty(false);
      setLoadingSteps(false);
      if (process.env.NODE_ENV === "development") {
        console.error("Error loading or rebalancing steps:", error);
      }
    }
  };

  const handleRebalance = async () => {
    try {
      setLoadingSteps(true);
      const fullUrl =
        useAt !== "Alarm SOP"
          ? `${dbConfig.url}/stepdata/rebalance/${maintenanceId}`
          : `${dbConfig.url}/alarmSopStepData/rebalance/${maintenanceId}`;
      await axios.put(fullUrl);
      toastMessageSuccess({message: "Steps rebalanced successfully."});
      await getAllSteps();
    } catch (error) {
      toastMessage({message: "Failed to rebalance steps", type: "error"});
      setLoadingSteps(false);
    }
  };

  const getAllSteps = async () => {
    try {
      setLoadingSteps(true);
      const fullUrl =
        useAt === "Alarm SOP"
          ? `${dbConfig.url}/alarmSopStepData/getAlarmSOPSteps/${maintenanceId}`
          : `${dbConfig.url}/stepdata/getManualSteps/${maintenanceId}`;
      const response = await axios.get(fullUrl);
      const sortedSteps = (response?.data?.data || []).sort(
        (a, b) => a.sortKey - b.sortKey,
      );
      setSteps(sortedSteps);
      originalStepsRef.current = sortedSteps;
      setDirty(false);
    } catch (error) {
      setSteps([]);
      originalStepsRef.current = [];
      setDirty(false);
    } finally {
      setLoadingSteps(false);
    }
  };

  const computeNewSortKey = (prev, next) => {
    if (prev == null && next == null) return 100;
    if (prev == null) return next.sortKey / 2;
    if (next == null) return prev.sortKey + 100;
    return (prev.sortKey + next.sortKey) / 2;
  };

  const handleDragEnd = result => {
    if (!result.destination) return;
    if (result.source.index === result.destination.index) return;

    const sorted = [...steps].sort((a, b) => a.sortKey - b.sortKey);
    const movedStep = sorted[result.source.index];
    const newSteps = [...sorted];
    newSteps.splice(result.source.index, 1);
    newSteps.splice(result.destination.index, 0, movedStep);

    const prev = newSteps[result.destination.index - 1] || null;
    const next = newSteps[result.destination.index + 1] || null;
    const newSortKey = computeNewSortKey(prev, next);

    const updatedStep = {...movedStep, sortKey: newSortKey};
    newSteps[result.destination.index] = updatedStep;
    setSteps(newSteps);
    setDirty(true);
  };

  const handleUpdate = async () => {
    try {
      setLoadingSteps(true);
      const original = originalStepsRef.current;
      const changed = steps.filter((step, idx) => {
        const orig = original.find(s => s._id === step._id);
        return !orig || orig.sortKey !== step.sortKey;
      });
      await Promise.all(
        changed.map(step =>
          axios.put(
            `${dbConfig.url}/${
              useAt === "Alarm SOP" ? "alarmSopStepData" : "stepdata"
            }/${step._id}`,
            {
              sortKey: step.sortKey,
            },
          ),
        ),
      );
      toastMessageSuccess({message: "Steps arrangement saved successfully"});
      await getAllSteps();
    } catch (err) {
      toastMessage({
        message: "Failed to save steps arrangement",
        type: "error",
      });
    } finally {
      setLoadingSteps(false);
    }
  };

  const needsRebalance = () => {
    if (!Array.isArray(steps) || steps.length < 2) return false;
    const sorted = [...steps].sort((a, b) => a.sortKey - b.sortKey);
    for (let i = 1; i < sorted.length; i++) {
      if (Math.abs(sorted[i].sortKey - sorted[i - 1].sortKey) < 1) {
        return true;
      }
    }
    return false;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      fullScreen={isMobile}
      PaperProps={{
        sx: {
          bgcolor: isDarkMode ? theme.palette.background.default : "",
          minHeight: isMobile ? "100vh" : "75vh",
          maxHeight: isMobile ? "100vh" : "85vh",
          minWidth: isMobile ? "100vw" : "600px",
          display: "flex",
          flexDirection: "column",
        },
      }}>
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          bgcolor: isDarkMode ? theme.palette.background.paper : "",
          color: isDarkMode ? theme.palette.text.primary : "",
          borderBottom: `1px solid ${isDarkMode ? theme.palette.divider : "#eee"}`,
          py: 2,
          px: 3,
        }}
      >
        <Box display="flex" alignItems="center">
          <ReorderIcon
            sx={{ mr: 2, color: theme.palette.primary.main, fontSize: 20 }}
          />
          <Typography variant="h6" fontWeight="300">
            Arrange Steps
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="medium" edge="end">
          <CloseIcon fontSize="medium" />
        </IconButton>
      </DialogTitle>

      <DialogContent
        sx={{
          bgcolor: isDarkMode ? theme.palette.background.default : "",
          p: 0,
          flex: 1,
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Box sx={{ flex: 1, overflow: "auto", p: 2 }}>
          {loadingSteps ? (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
              }}>
              <CircularProgress size={24} />
            </Box>
          ) : Array.isArray(steps) && steps.length > 0 ? (
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="steps-list">
                {provided => (
                  <Box
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    sx={{ display: "flex", flexDirection: "column", gap: 1 }}
                  >
                    {steps
                      .sort((a, b) => a.sortKey - b.sortKey)
                      .map((step, idx) => (
                        <Draggable
                          key={step._id || idx}
                          draggableId={step._id || String(idx)}
                          index={idx}>
                          {(provided, snapshot) => (
                            <Box
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              sx={{
                                background: snapshot.isDragging
                                  ? isDarkMode
                                    ? theme.palette.background.paper
                                    : theme.palette.grey[100]
                                  : isDarkMode
                                  ? theme.palette.background.paper
                                  : "#fff",
                                borderRadius: 1,
                                border: `1px solid ${
                                  isDarkMode
                                    ? theme.palette.divider
                                    : theme.palette.grey[200]
                                }`,
                                boxShadow: snapshot.isDragging
                                  ? theme.shadows[1]
                                  : "none",
                                ...provided.draggableProps.style,
                              }}>
                              <ListItem
                                sx={{ py: 0.8, px: 1.5, alignItems: "center" }}
                              >
                                <Box
                                  {...provided.dragHandleProps}
                                  sx={{ display: "flex", mr: 2 }}
                                >
                                  <DragHandleIcon fontSize="medium" />
                                </Box>
                                <Box sx={{flex: 1, overflow: "hidden"}}>
                                  <Typography
                                    variant="body1"
                                    sx={{
                                      fontWeight: 500,
                                      whiteSpace: "nowrap",
                                      overflow: "hidden",
                                      textOverflow: "ellipsis",
                                      lineHeight: 1.3,
                                      fontSize: "1rem",
                                    }}
                                  >
                                    {step.title}
                                  </Typography>
                                </Box>
                                <Box
                                  sx={{
                                    width: 28,
                                    height: 28,
                                    borderRadius: "50%",
                                    border: `2px solid ${theme.palette.primary.main}`,
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    ml: 2,
                                    color: theme.palette.primary.main,
                                    fontSize: "0.7rem",
                                    fontWeight: 600,
                                  }}
                                >
                                  {idx + 1}
                                </Box>
                              </ListItem>
                            </Box>
                          )}
                        </Draggable>
                      ))}
                    {provided.placeholder}
                  </Box>
                )}
              </Droppable>
            </DragDropContext>
          ) : (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
              }}
            >
              <Typography variant="h6" color="text.secondary">No steps found</Typography>
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions
        sx={{
          bgcolor: isDarkMode ? theme.palette.background.paper : "",
          borderTop: `1px solid ${isDarkMode ? theme.palette.divider : "#eee"}`,
          p: 2,
        }}
      >
        {needsRebalance() && (
          <Tooltip title="Evenly redistribute sort keys">
            <ButtonBasic
              buttonTitle="Rebalance"
              onClick={handleRebalance}
              variant="outlined"
              color="warning"
              disabled={loadingSteps}
              startIcon={<RebalanceIcon fontSize="medium" />}
              size="medium"
            />
          </Tooltip>
        )}
        <Box sx={{ flex: 1 }} />
        <ButtonBasicCancel
        buttonTitle="Cancel"
          onClick={onClose}
          // variant="outlined"
          disabled={loadingSteps}
          size="medium"
          // sx={{ mr: 1, minWidth: 80 }}
        />
        <Tooltip title={!dirty ? "Make changes to enable save" : ""}>
          <span>
            <SubmitButtons
              buttonTitle="Save"
              onClick={handleUpdate}
              variant="contained"
              disabled={!dirty || loadingSteps}
              startIcon={<SaveIcon fontSize="medium" />}
              size="medium"
              // sx={{ minWidth: 100 }}
            />
          </span>
        </Tooltip>
      </DialogActions>
    </Dialog>
  );
};

export default ArrangeStepsDialog;
