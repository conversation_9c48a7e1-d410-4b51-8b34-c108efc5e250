
export const toHHMM = (minutes) => {
  const hrs = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hrs.toString().padStart(2, "0")}:${mins
    .toString()
    .padStart(2, "0")}`;
};

export const fromHHMM = (value) => {
  const [hrs, mins] = value.split(":").map(Number);
  return hrs * 60 + mins;
};

export const formatDateShort = (date) => {
  const d = new Date(date);
  const day = d.getDate().toString().padStart(2, "0");
  const month = (d.getMonth() + 1).toString();
  return `${day}-${month}`;
};

export const isValidHHMM = (value) => {
  const regex = /^([0-1]?\d|2[0-3]):[0-5]\d$/;
  return regex.test(value);
};

export const toDDMMYYYY = (date) => {
  if (!date) return "";
  try {
    let parsedDate;
    // Handle YYYY-MM-DD format
    if (typeof date === "string" && /^\d{4}-\d{2}-\d{2}$/.test(date)) {
      const [year, month, day] = date.split("-").map(Number);
      parsedDate = new Date(year, month - 1, day);
    }
    // Handle ISO format (e.g., 2025-05-23T00:00:00.000Z)
    else if (typeof date === "string" && date.includes("T")) {
      parsedDate = new Date(date);
    } else {
      parsedDate = new Date(date);
    }
    if (isNaN(parsedDate)) return "";
    return `${parsedDate.getDate().toString().padStart(2, "0")}/${(
      parsedDate.getMonth() + 1
    )
      .toString()
      .padStart(2, "0")}/${parsedDate.getFullYear()}`;
  } catch {
    return "";
  }
};

export const isValidDDMMYYYY = (dateStr) => {
  if (!dateStr || typeof dateStr !== "string") return false;
  const regex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
  if (!regex.test(dateStr)) return false;
  const [day, month, year] = dateStr.split("/").map(Number);
  const date = new Date(year, month - 1, day);
  return (
    date.getDate() === day &&
    date.getMonth() === month - 1 &&
    date.getFullYear() === year
  );
};

export const toYYYYMMDD = (value) => {
  if (!value) return "";
  const [day, month, year] = value.split("/");
  return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
};

export const toTitleCase = (str) =>
  str
    .toString()
    .replace(/_/g, " ")
    .replace(/\b\w/g, (char) => char.toUpperCase());


// Get yesterday and today for date selection
export const getAllowedDates = () => {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  return [toDDMMYYYY(yesterday), toDDMMYYYY(today)];
};

// Calculate time difference in minutes between start_time and end_time
export const calculateTimeTaken = (startTime, endTime) => {
  if (!isValidHHMM(startTime) || !isValidHHMM(endTime)) return "";
  const [startHours, startMinutes] = startTime.split(":").map(Number);
  const [endHours, endMinutes] = endTime.split(":").map(Number);
  const startTotalMinutes = startHours * 60 + startMinutes;
  const endTotalMinutes = endHours * 60 + endMinutes;
  const diff = endTotalMinutes - startTotalMinutes;
  return diff >= 0 ? String(diff) : "";
};

// Color palette for reasons
export const reasonColors = [
  "#FF6B6B",
  "#4ECDC4",
  "#45B7D1",
  "#96CEB4",
  "#FFEEAD",
  "#D4A5A5",
  "#9B59B6",
  "#3498DB",
];