import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
} from "@mui/material";
import React, {useState} from "react";
import {
  companies,
  companyId_constant,
  maintenance,
} from "../../../constants/data";
import {db} from "../../../firebase";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../../tools/toast";
import {useStateContext} from "../../../context/ContextProvider";
import {
  ButtonBasic,
  ButtonBasicCancel,
  SubmitButtons,
} from "../../../components/buttons/Buttons";
import axios from "axios";
import {dbConfig} from "../../../infrastructure/db/db-config";
import {useMongoRefresh} from "../../../services/mongo-refresh.context";
import {useEditMachineCfr} from "../../../hooks/cfr/machineCfrProvider";
import {useAuth} from "../../../hooks/AuthProvider";
import {DropzoneArea} from "material-ui-dropzone";
import {convertBase64} from "../../../hooks/useBase64";
import GetPreviewComponent from "../../../components/commons/getPreview.component";

const EditMaintenance = ({
  mid,
  handleClose,
  data,
  machineName,
  MaintenanceTitle,
  userName,
  useAt = "",
  handleSubmitSuccess,
}) => {
  const {currentUser} = useAuth();

  const [title, setTitle] = useState(data.title);
  const [desc, setDesc] = useState(data.desc);
  const [period, setPeriod] = useState(data.cycle_time);
  const [type, setType] = useState(data.type);
  const [status, setStatus] = useState(data.status ? data.status : false);
  const {refreshCount, setRefreshCount} = useMongoRefresh();
  const [imageUrl, setImageUrl] = useState("");
  const [selectedFileForStorage, setSelectedFileForStorage] = useState("");
  const [imageFileUrl, setImageFileUrl] = useState("");
  const [selectedImageForStorage, setSelectedImageForStorage] = useState("");
  const [frequencies, setFrequencies] = useState([
    "Daily",
    "Every 1 Month",
    "Every 3 Months",
    "Every 6 Month",
    "Every 1 Year",
    "Every 2 Years",
    "Every 3 Years",
    "Every 5 Years",
  ]);
  const {currentColor, currentMode, currentColorLight} = useStateContext();
  const editmaintenancecfr = useEditMachineCfr();

  const typesImages = ["application/pdf"];
  const typesImagesImage = [
    "image/png",
    "image/jpeg",
    "image/jpg",
    "image/webp",
  ];
  //
  const handleChange = async loadedFiles => {
    let selectedFile = loadedFiles[0];
    const base64 = await convertBase64(selectedFile);
    setSelectedFileForStorage(selectedFile); // for uploading to storage

    if (typesImages.includes(selectedFile.type)) {
      setImageUrl(base64);
    } else {
      toastMessage({message: "Please select a PDF"});
    }
  };
  const handleImageChange = async loadedFiles => {
    let selectedFile = loadedFiles[0];
    if (!selectedFile) {
      setImageFileUrl("");
      setSelectedImageForStorage("");
      return;
    }
    const base64 = await convertBase64(selectedFile);
    setSelectedImageForStorage(selectedFile); // for uploading to storage

    if (typesImagesImage.includes(selectedFile.type)) {
      setImageFileUrl(base64);
    } else {
      toastMessage({
        message: "Please select an image file (png, jpg, jpeg, webp)",
      });
    }
  };
  //
  const handleSubmit = async e => {
    e.preventDefault();

    if (selectedFileForStorage && data.sop_url) {
      await axios
        .post(`${dbConfig?.url_storage}/deleteImage`, {
          file_name: data.sop_url,
        })
        .then(res1 => {
          console.log(res1.data?.message, "updated successfully");
        })
        .catch(err => {
          console.log("delete file from storage err:", err);
        });
    }
    // handle delete old image if new image selected
    if (selectedImageForStorage && data.image_url) {
      await axios
        .post(`${dbConfig?.url_storage}/deleteImage`, {
          file_name: data.image_url,
        })
        .then(res1 => {
          console.log(res1.data?.message, "image updated successfully");
        })
        .catch(err => {
          console.log("delete image from storage err:", err);
        });
    }
    const date = new Date();

    // Account for Alarm SOP
    const activityText =
      useAt === "AlarmSop" ? "Alarm SOP edited" : "Maintenance edited";
    const descriptionText =
      useAt === "AlarmSop"
        ? "an Alarm SOP is edited"
        : "a Maintenance is edited";
    const moduleText = useAt === "AlarmSop" ? "Alarm SOP" : "Maintenance";

    const data2 = {
      activity: activityText,
      dateTime: date,

      description: descriptionText,
      machine: mid,
      module: moduleText,
      username: currentUser.username,
    };
    const dataSet = {
      ...data,
      title,
      desc: desc,
      cycle_time: period,
      created_at: data.created_at,
      last_updated: new Date(),
      mid,
      type,
      status,
    };

    const editAlarmSopData = {
      ...data,
      title,
      desc,
      status,
    };

    let resTemp = null;
    let resImageTemp = null;

    if (selectedFileForStorage) {
      let fd = new FormData();
      fd.append("image", selectedFileForStorage);
      resTemp = await axios
        .post(`${dbConfig?.url_storage}/upload`, fd, {})
        .then(res1 => {
          console.log("storage:", res1);
          return res1;
        })
        .catch(err => {
          console.log("storage error:", err);
        });
    }
    if (selectedImageForStorage) {
      let fd = new FormData();
      fd.append("image", selectedImageForStorage);
      resImageTemp = await axios
        .post(`${dbConfig?.url_storage}/upload`, fd, {})
        .then(res1 => {
          console.log("storage image:", res1);
          return res1;
        })
        .catch(err => {
          console.log("storage image error:", err);
        });
    }

    // Being used at both alarmSop and maintenance
    const payload =
      useAt !== "AlarmSop"
        ? {
            ...dataSet,
            sop_url: selectedFileForStorage ? resTemp?.data?.data : "",
            image_url: selectedImageForStorage
              ? resImageTemp?.data?.data
              : data.image_url || "",
          }
        : {
            ...editAlarmSopData,
            sop_url: selectedFileForStorage ? resTemp?.data?.data : "",
            image_url: selectedImageForStorage
              ? resImageTemp?.data?.data
              : data.image_url || "",
          };

    await axios
      .put(
        `${dbConfig.url}/${useAt === "AlarmSop" ? "alarmSop" : "maintenance"}/${
          data._id
        }`,
        payload,
      )
      .then(() => {
        toastMessageSuccess({
          message: `${dataSet?.title}  updated successfully`,
        });
        editmaintenancecfr(data2);
        if (useAt === "AlarmSop") {
          handleSubmitSuccess();
        }
        handleClose();
        setRefreshCount(refreshCount + 1);
      })
      .catch(err => {
        toastMessage({message: err.message});
      });
  };
  return (
    <form onSubmit={handleSubmit}>
      <InputLabel style={{marginBottom: "10px"}}>Title</InputLabel>
      <TextField
        onChange={e => setTitle(e.target.value)}
        onBlur={() => setTitle(title?.trim())}
        value={title}
        required
        variant="outlined"
        fullWidth
        style={{marginBottom: "12px"}}
      />
      <InputLabel style={{marginBottom: "10px"}}>Description</InputLabel>
      <TextField
        onChange={e => setDesc(e.target.value)}
        onBlur={() => setDesc(desc?.trim())}
        value={desc}
        required
        variant="outlined"
        fullWidth
        multiline
        style={{marginBottom: "12px"}}
      />
      {useAt !== "AlarmSop" && (
        <>
          {" "}
          <InputLabel style={{marginBottom: "10px"}}>Select Type</InputLabel>
          <FormControl
            style={{marginBottom: "10px"}}
            required
            variant="outlined"
            fullWidth>
            <Select
              required
              value={type}
              onChange={e => setType(e.target.value)}>
              {type === 0 ? (
                <MenuItem
                  style={
                    currentMode === "Dark"
                      ? {backgroundColor: "#212B36", color: "white"}
                      : {}
                  }
                  value={0}>
                  Calibration
                </MenuItem>
              ) : type === 4 ? (
                <MenuItem
                  style={
                    currentMode === "Dark"
                      ? {backgroundColor: "#212B36", color: "white"}
                      : {}
                  }
                  value={4}>
                  Gemba
                </MenuItem>
              ) : type === 5 ? (
                <MenuItem
                  style={
                    currentMode === "Dark"
                      ? {backgroundColor: "#212B36", color: "white"}
                      : {}
                  }
                  value={5}>
                  Line Clearance
                </MenuItem>
              ) : (
                [
                  <MenuItem
                    key={1}
                    style={
                      currentMode === "Dark"
                        ? {backgroundColor: "#212B36", color: "white"}
                        : {}
                    }
                    value={1}>
                    Machine Breakdown
                  </MenuItem>,
                  <MenuItem
                    key={2}
                    style={
                      currentMode === "Dark"
                        ? {backgroundColor: "#212B36", color: "white"}
                        : {}
                    }
                    value={2}>
                    Routine
                  </MenuItem>,
                  <MenuItem
                    key={3}
                    style={
                      currentMode === "Dark"
                        ? {backgroundColor: "#212B36", color: "white"}
                        : {}
                    }
                    value={3}>
                    Preventive
                  </MenuItem>,
                ]
              )}
            </Select>
          </FormControl>
          <InputLabel
            style={{marginBottom: "10px", display: type == 1 ? "none" : ""}}>
            Select Period
          </InputLabel>
          <FormControl
            style={{marginBottom: "10px", display: type == 1 ? "none" : ""}}
            required
            variant="outlined"
            fullWidth>
            <Select
              required
              value={period}
              onChange={e => setPeriod(e.target.value)}>
              {frequencies.map(title => (
                <MenuItem
                  style={
                    currentMode === "Dark"
                      ? {backgroundColor: "#212B36", color: "white"}
                      : {}
                  }
                  value={title}
                  key={title}>
                  {title}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </>
      )}

      <InputLabel style={{marginBottom: "10px"}}>Select Status</InputLabel>
      <FormControl
        style={{marginBottom: "10px"}}
        required
        variant="outlined"
        fullWidth>
        <Select
          required
          value={status}
          onChange={e => setStatus(e.target.value)}>
          <MenuItem
            value={true}
            style={
              currentMode === "Dark"
                ? {backgroundColor: "#212B36", color: "white"}
                : {}
            }>
            ON
          </MenuItem>
          <MenuItem
            value={false}
            style={
              currentMode === "Dark"
                ? {backgroundColor: "#212B36", color: "white"}
                : {}
            }>
            OFF
          </MenuItem>
        </Select>

        <div className="text-2xl text-gray-700 flex justify-end">
          {/* <p> {progress} % Uploaded</p> */}
          <br />
        </div>
        <>
          {/* <InputLabel style={{ marginBottom: "10px" }}>Media</InputLabel> */}

          <Tooltip
            title={data?.sop_url ? "Update with new SOP" : "Add a new SOP"}
            placement="top">
            <span> {data?.sop_url ? "Update SOP" : "Add SOP"}</span>
          </Tooltip>
          <DropzoneArea
            acceptedFiles={["application/pdf"]}
            showFileNames={true}
            onChange={loadedFiles => handleChange(loadedFiles)}
            dropzoneText="Drag and Drop / Click to ADD Media"
            showAlerts={false}
            filesLimit={1}
            maxFileSize={50 * 1024 * 1024} // bytes :"default: 3000000"
            onDelete={() => handleChange("")}
            // onDelete={() => DeleteByUrl(url)} // delete from storage when file selection canceled by clicking cross button
          />

          {/* Conditionally render GetPreviewComponent only if imageUrl or data?.sop_url exists */}
          {(imageUrl || data?.sop_url) && (
            <div
              className="my-2"
              style={{display: "flex", justifyContent: "center"}}>
              <GetPreviewComponent
                sourceUrl={
                  imageUrl.length > 0
                    ? imageUrl
                    : data?.sop_url
                    ? `${dbConfig?.url_storage}/${data?.sop_url}`
                    : ""
                }
                fileFormat="pdf"
                previewPdfStyle={{width: "450px", height: "500px"}}
                previewImageStyle={{width: "450px"}}
                previewVideoStyle={{width: "90%", marginTop: "20px"}}
                previewAudioStyle={{
                  marginTop: "15%",
                  marginRight: "50px",
                  marginBottom: "20px",
                }}
              />
            </div>
          )}
          {/* Add Image field */}
          <Tooltip
            title={
              data?.image_url ? "Update with new Image" : "Add a new Image"
            }
            placement="top">
            <span> Add Image</span>
          </Tooltip>
          <DropzoneArea
            acceptedFiles={[
              "image/png",
              "image/jpeg",
              "image/jpg",
              "image/webp",
            ]}
            showFileNames={true}
            onChange={loadedFiles => handleImageChange(loadedFiles)}
            dropzoneText="Drag and Drop / Click to ADD Image"
            showAlerts={false}
            filesLimit={1}
            maxFileSize={50 * 1024 * 1024}
            onDelete={() => handleImageChange("")}
          />
          {(imageFileUrl || data?.image_url) && (
            <div
              className="my-2"
              style={{display: "flex", justifyContent: "center"}}>
              <GetPreviewComponent
                sourceUrl={
                  imageFileUrl.length > 0
                    ? imageFileUrl
                    : data?.image_url
                    ? `${dbConfig?.url_storage}/${data?.image_url}`
                    : ""
                }
                fileFormat="image"
                previewImageStyle={{width: "450px"}}
              />
            </div>
          )}
        </>
      </FormControl>

      <div className="p-2 mt-2 flex justify-between">
        <ButtonBasicCancel buttonTitle="Cancel" onClick={() => handleClose()} />
        <SubmitButtons buttonTitle="Submit" type="submit" />
      </div>
    </form>
  );
};

export default EditMaintenance;
