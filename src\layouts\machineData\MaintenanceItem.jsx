/* eslint-disable no-duplicate-case */
/* eslint-disable default-case */
import React, {
  useState,
  useRef,
  useEffect,
  useContext,
  useCallback,
} from "react";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  MenuItem,
  Select,
  Button,
  TableRow,
  TableCell,
  IconButton,
  Badge,
  Tooltip,
  InputLabel,
  TextField,
  Box,
  FormControl,
  Autocomplete,
  Menu,
  MenuItem as MuiMenuItem,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import {Link, useNavigate} from "react-router-dom";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import Delete from "../../components/Delete/Delete";
import EditMaintenance from "./EditMaintenance/EditMaintenance";
import Manuals from "../Manuals/Manuals";
import BugReportIcon from "@mui/icons-material/BugReport";
import {useAuth} from "../../hooks/AuthProvider";
import LoggingFunction from "../../components/CFR-Report/LoggingFunction";
import {useMaintenanceInfo} from "../../context/MaintenanceContext";
import {themeColors} from "../../infrastructure/theme";
import axios from "axios";
import {dbConfig} from "../../infrastructure/db/db-config";
import {useMongoRefresh} from "../../services/mongo-refresh.context";
import {useIssueModuleProjectData} from "../../services2/issueModule/IssueModule.context";
import {makeStyles} from "@mui/styles";
import {useDeleteMachineCfr} from "../../hooks/cfr/machineCfrProvider";
import {ButtonBasicCancel} from "../../components/buttons/Buttons";
import {HashtagContext} from "../../services2/hashtag/hashtag.context";
import {
  AccountTree,
  AddCircleOutlineOutlined,
  PictureAsPdf,
  ReceiptLong,
  ViewList,
} from "@mui/icons-material";
import ReportDialog from "./ReportDialog";
import SopPreviewDialog from "./SopPreviewDialog";
import AddStepDialog from "./AddStepDialog";
import ArrangeStepsDialog from "./ArrangeStepsDialog";
import {useStateContext} from "../../context/ContextProvider";
import {useCheckAccess} from "../../utils/useCheckAccess";
import HandleAlarmSop from "./AlarmNew&Manager/HandleAlarmSop";

const useCustomStyles = makeStyles(theme => ({
  manualsContainer: {
    padding: "0.5rem",
    backgroundColor: theme.palette.custom.backgroundForth,
    height: "auto",
  },
  stepsContainer: {
    padding: "1rem",
    backgroundColor: theme.palette.custom.backgroundForth,
    marginTop: "0.5rem",
  },
}));

const scrollToRef = ref => window.scrollTo(0, ref.current.offsetTop);

const GetColor = ({type}) => {
  if (type == 0) {
    return (
      <div className="flex ">
        <div
          style={{
            width: "3px",
            backgroundColor: "red",
            padding: "5px",
          }}></div>
        <p className="uppercase">CALIBRATION</p>
      </div>
    );
  }
  if (type == 1) {
    return (
      <div className="flex ">
        <div
          style={{
            width: "3px",
            backgroundColor: "green",
            padding: "5px",
          }}></div>
        <p className="uppercase">EXCEPTION</p>
      </div>
    );
  }
  if (type == 2) {
    return (
      <div className="flex ">
        <div
          style={{
            width: "3px",
            backgroundColor: "blue",
            padding: "5px",
          }}></div>
        <p className="uppercase">ROUTINE</p>
      </div>
    );
  }
};

const MaintenanceItem = ({
  data,
  machineName,
  defaultType,
  useAt = "",
  lastItem = false,
  expandedByDefault = false,
  onAlarmSopEditOrDeleteSuccess = () => {},
}) => {
  const {currentUser} = useAuth();
  const [user, setUser] = useState([]);
  const [isOpen, setIsOpen] = useState(expandedByDefault);
  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openAssignIssue, setOpenAssignIssue] = useState(false);
  const scrollRef = useRef(null);
  const [issueId, setIssueId] = useState(data?.issue_id);
  const [changeBool, setChangeBool] = useState(true);
  const customCss = useCustomStyles();
  const issues = useIssueModuleProjectData();
  const [open, setOpen] = useState(false);
  const [steps, setSteps] = useState([]);
  const {refreshCount, setRefreshCount} = useMongoRefresh();
  const {hashes, handleHastageData, setTempHashes, hashArray, temptemp} =
    useContext(HashtagContext);
  const parent = useAt !== "Alarm SOP" ? "Maintenance" : useAt;
  const {currentColor, currentMode, currentColorLight} = useStateContext();
  const manual_id = data?._id;
  const alarm_sop_id = data?._id;
  const [openReportConfirm, setOpenReportConfirm] = useState(false);
  const [openSop, setOpenSop] = useState(false);
  const [openArrangeSteps, setOpenArrangeSteps] = useState(false);

  const [anchorEl, setAnchorEl] = useState(null);
  const openMenu = Boolean(anchorEl);

  const hasMainDELETEAccess = useCheckAccess("maintenance", "DELETE");
  const hasMainPUTAccess = useCheckAccess("maintenance", "PUT");

  const hasStepPOSTAccess = useCheckAccess("stepdata", "POST");
  const hasMainReportPOSTAccess = useCheckAccess("mainReportData", "POST");
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const handleMenuOpen = event => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Handle report generation
  const handleGenerateReport = async () => {
    try {
      const reportData = {
        title: data.title,
        desc: data.desc,
        type: data.type,
        cycle_time: data.cycle_time,
        last_done: data.last_done,
        sop_url: data.sop_url,
        status: data.status,
        mid: data.mid,
        issue_id: data.issue_id,
        visit: data.visit,
        created_at: new Date(),
        main_id: data._id,
        email: currentUser.email,
      };

      const alarmSopReportData = {
        title: data.title,
        desc: data.desc,
        sop_url: data.sop_url,
        mid: data.mid,
        created_at: new Date(),
        alarm_sop_id: data._id,
        email: currentUser.email,
      };

      const fullUrl =
        useAt !== "Alarm SOP"
          ? `${dbConfig.url}/mainReportData/create-report`
          : `${dbConfig.url}/alarmReport/create-report`;
      const payload = useAt !== "Alarm SOP" ? reportData : alarmSopReportData;

      const response = await axios.post(fullUrl, payload);

      if (response.data.success) {
        toastMessageSuccess({
          message: response.data.message || "Report generated successfully!",
        });
        setRefreshCount(refreshCount + 1);
      }
      setOpenReportConfirm(false);
    } catch (error) {
      toastMessage({
        message: error.response?.data?.message || "Failed to generate report",
      });
    }
  };

  const cycleArr = [
    "Daily",
    "Every 1 Month",
    "Every 3 Months",
    "Every 6 Months",
    "Every 1 Year",
    "Every 2 Years",
    "Every 3 Years",
    "Every 5 Years",
  ];

  const maintenanceInfoFromContext = useMaintenanceInfo();
  useEffect(() => {
    if (maintenanceInfoFromContext?.maintenance_id == data?._id) {
      setIsOpen(true);
    } else if (expandedByDefault) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [maintenanceInfoFromContext, expandedByDefault]);

  const history = useNavigate();

  const executeScroll = () => scrollToRef(scrollRef);

  const deletemaintenancecfr = useDeleteMachineCfr();

  const deleteData = async () => {
    const date = new Date();
    const data2 = {
      activity: "Maintenance deleted",
      dateTime: date,
      description: "a Maintenance is deleted",
      machine: data?._id,
      module: "Maintenance",
      username: currentUser.username,
    };

    try {
      // Delete the maintenance record and related hashtags in parallel
      await Promise.all([
        axios.delete(`${dbConfig.url}/hashtags/maintenance/${data._id}`),
        axios.delete(`${dbConfig.url}/maintenance/${data._id}`),
      ]);

      // Log the deletion activity
      deletemaintenancecfr(data2);

      // Update UI states
      setOpenDel(false);
      toastMessageSuccess({
        message: `Deleted ${data?.title} data and related hashtags successfully!`,
      });
      setRefreshCount(refreshCount + 1);
    } catch (err) {
      toastMessage({message: err.message});
    }
  };

  const deleteAlarmsSopData = async () => {
    const date = new Date();
    // Account for Alarm SOP
    const activity = "Alarm SOP deleted";
    const description = "an Alarm SOP is deleted";
    const module = "Alarm SOP";

    const alarmSopDeleteData = {
      activity,
      dateTime: date,
      description,
      machine: data?._id,
      module,
      username: currentUser.username,
    };
    try {
      await axios.delete(`${dbConfig.url}/alarmSop/${data._id}`);

      // Log the deletion activity
      deletemaintenancecfr(alarmSopDeleteData);

      setOpenDel(false);
      toastMessageSuccess({
        message: `Deleted ${data?.title} alarm SOP data successfully!`,
      });
      onAlarmSopEditOrDeleteSuccess();
    } catch (error) {
      toastMessage({message: error.message});
    }
  };

  const handelOpenClose = () => {
    setIsOpen(!isOpen);
  };

  const onBugIconClick = () => {
    setOpenAssignIssue(true);
    setChangeBool(true);
  };

  const saveIssueId = async () => {
    const dataSet = {
      ...data,
      issue_id: issueId,
    };

    await axios
      .put(`${dbConfig.url}/maintenance/${data._id}`, dataSet)
      .then(() => {
        toastMessageSuccess({
          message: `Updated Issue to Maintenance - ${data?.title} successfully !`,
        });
        setOpenAssignIssue(false);
        setRefreshCount(refreshCount + 1);
      })
      .catch(err => {
        toastMessage({message: err.message});
      });
  };

  const onIssueSelection = e => {
    setIssueId(e.target.value);
    if (data.issue_id === e.target.value) {
      setChangeBool(true);
    } else {
      setChangeBool(false);
    }
    if (e.target.value === "newIssue") {
      history("/flow-chart");
    }
  };

  const issueRedirect = () => {
    let temp = null;
    issues.map(issue => {
      if (issue._id === data.issue_id) {
        temp = (
          <Link
            to={{
              pathname: "/edit",
              mid: issue.mid,
              state: issue.values,
              id: issue._id,
              name: issue.name,
            }}
            className="view-project-btn">
            <Button size="small" variant="contained">
              <span className="animate-pulse">{issue.name} </span>
              <i className="ri-eye-line"></i>
            </Button>
          </Link>
        );
      }
    });
    return temp;
  };

  const alarm = data => {
    let today = new Date();
    let temp = null;
    const alarmIcon = (last_done, days) => {
      let lastDoneDate = last_done?.toDate();
      let expiredOn = lastDoneDate.setDate(
        (new Date(lastDoneDate).getDate() + days).toString(),
      );
      let expiredTodate = new Date(expiredOn);
      return (
        <>
          <span
            data-title={
              "Expired on :" + expiredTodate.toString().substring(0, 15)
            }>
            <i className="ri-alarm-fill text-red-600 text-xl"></i>
          </span>
        </>
      );
    };

    switch (data.cycle_time) {
      case cycleArr[0]: // daily
        if (
          1 <
          Math.abs(Math.ceil((today - data.last_done) / (1000 * 60 * 60 * 24)))
        ) {
          temp = alarmIcon(data.last_done, 1);
        }
        break;
      case cycleArr[1]: // 1 month
        if (
          1 <
          Math.abs(
            Math.ceil((today - data.last_done) / (1000 * 60 * 60 * 24 * 30)),
          )
        ) {
          temp = alarmIcon(data.last_done, 1 * 30);
        }
        break;
      case cycleArr[2]: // 3 months
        if (
          3 <
          Math.abs(
            Math.ceil((today - data.last_done) / (1000 * 60 * 60 * 24 * 30)),
          )
        ) {
          temp = alarmIcon(data.last_done, 1 * 30 * 3);
        }
        break;
      case cycleArr[3]: // 6 months
        if (
          6 <
          Math.abs(
            Math.ceil((today - data.last_done) / (1000 * 60 * 60 * 24 * 30)),
          )
        ) {
          temp = alarmIcon(data.last_done, 1 * 30 * 6);
        }
        break;
      case cycleArr[4]: // 1 Year
        if (
          12 <
          Math.abs(
            Math.ceil((today - data.last_done) / (1000 * 60 * 60 * 24 * 30)),
          )
        ) {
          temp = alarmIcon(data.last_done, 1 * 30 * 12);
        }
        break;
      case cycleArr[5]: // 2 Years
        if (
          2 <
          Math.abs(
            Math.ceil(
              (today - data.last_done) / (1000 * 60 * 60 * 24 * 30 * 12),
            ),
          )
        ) {
          temp = alarmIcon(data.last_done, 1 * 30 * 12 * 2);
        }
        break;
      case cycleArr[6]: // 3 Years
        if (
          3 <
          Math.abs(
            Math.ceil(
              (today - data.last_done) / (1000 * 60 * 60 * 24 * 30 * 12),
            ),
          )
        ) {
          temp = alarmIcon(data.last_done, 1 * 30 * 12 * 3);
        }
        break;
      case cycleArr[7]: // 5 Years
        if (
          5 <
          Math.abs(
            Math.ceil(
              (today - data.last_done) / (1000 * 60 * 60 * 24 * 30 * 12),
            ),
          )
        ) {
          temp = alarmIcon(data.last_done, 1 * 30 * 12 * 5);
        }
        break;
    }
    return temp;
  };

  let dueDate = new Date(data?.dueDate).toString();
  dueDate = dueDate?.substring(0, 15);

  return (
    <>
      <TableRow
        hover
        sx={{
          "&:last-child td, &:last-child th": {border: 0},
          borderBottom: lastItem ? "none" : "0.05rem solid #e0e0e0",
          "&:hover": {bgcolor: "#f5f5f5"},
        }}
        style={{cursor: "pointer"}}>
        <TableCell
          style={
            currentMode === "Dark"
              ? {color: "white", borderBottom: "none"}
              : {color: "black", borderBottom: "none"}
          }
          align="left">
          <b className="capitalize">{data?.title}</b>
        </TableCell>

        <TableCell
          style={
            currentMode === "Dark"
              ? {color: "white", borderBottom: "none"}
              : {color: "black", borderBottom: "none"}
          }
          align="left">
          {data?.desc}
        </TableCell>

        {parseInt(data.type) !== 1 ? (
          <TableCell
            style={{borderBottom: "none", color: "green"}}
            align="left">
            {data?.cycle_time}
          </TableCell>
        ) : null}
        <TableCell
          style={
            currentMode === "Dark"
              ? {color: "white", borderBottom: "none"}
              : {color: "black", borderBottom: "none"}
          }
          align="left">
          <span>
            {data?.last_done &&
              new Intl.DateTimeFormat("en-GB", {
                day: "2-digit",
                month: "short",
                year: "numeric",
              }).format(new Date(data.last_done))}
          </span>
          {alarm(data)}
        </TableCell>

        <TableCell
          style={
            currentMode === "Dark"
              ? {color: "white", borderBottom: "none"}
              : {borderBottom: "none"}
          }
          align="center">
          {<Badge color={data?.status ? "success" : "error"} variant="dot" />}
        </TableCell>

        <TableCell
          style={
            currentMode === "Dark"
              ? {color: "white", borderBottom: "none"}
              : {color: "black", borderBottom: "none"}
          }
          align="center">
          <div className="flex items-center justify-center gap-2">
            <IconButton
              onClick={() => setOpenEdit(true)}
              disabled={!hasMainPUTAccess}
              sx={{
                color: !hasMainPUTAccess ? "grey.500" : "primary.main",
              }}>
              <EditIcon style={{fontSize: "20px"}} />
            </IconButton>

            <IconButton onClick={handleMenuOpen}>
              <MoreVertIcon
                style={{
                  fontSize: "20px",
                  color: currentMode === "Dark" ? "#fff" : "#000",
                }}
              />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={openMenu}
              onClose={handleMenuClose}
              slotProps={{
                paper: {
                  style: {
                    backgroundColor:
                      currentMode === "Dark" ? "#212B36" : "#fff",
                    color: currentMode === "Dark" ? "#fff" : "#000",
                  },
                },
              }}>
              <MuiMenuItem
                onClick={() => {
                  setOpenDel(true);
                  handleMenuClose();
                }}
                disabled={!hasMainDELETEAccess}>
                <DeleteIcon
                  style={{
                    fontSize: "20px",
                    color: "#f00",
                    marginRight: "8px",
                  }}
                />
                Delete
              </MuiMenuItem>
              {data.type === 1 && (
                <MuiMenuItem
                  onClick={() => {
                    onBugIconClick();
                    handleMenuClose();
                  }}>
                  <AccountTree style={{fontSize: "20px", marginRight: "8px"}} />
                  Assign Flow
                </MuiMenuItem>
              )}
              <MuiMenuItem
                onClick={() => {
                  setOpen(true);
                  handleMenuClose();
                }}
                disabled={!hasStepPOSTAccess}>
                <AddCircleOutlineOutlined
                  style={{fontSize: "20px", marginRight: "8px"}}
                />
                Add Step
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenArrangeSteps(true);
                  handleMenuClose();
                }}>
                <ViewList style={{fontSize: "20px", marginRight: "8px"}} />
                Arrange steps
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenReportConfirm(true);
                  handleMenuClose();
                }}
                disabled={!hasMainReportPOSTAccess}>
                <ReceiptLong style={{fontSize: "20px", marginRight: "8px"}} />
                Create Report
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenSop(true);
                  handleMenuClose();
                }}
                disabled={!data?.sop_url}>
                <PictureAsPdf
                  style={{
                    fontSize: "20px",
                    marginRight: "8px",
                    color: data?.sop_url
                      ? currentMode === "Dark"
                        ? "#fff"
                        : "red"
                      : "#ccc",
                  }}
                />
                View SOP
              </MuiMenuItem>
            </Menu>
            <IconButton onClick={() => handelOpenClose()}>
              {isOpen ? (
                <ExpandLessIcon style={{fontSize: "20px"}} />
              ) : (
                <ExpandMoreIcon style={{fontSize: "20px"}} />
              )}
            </IconButton>
          </div>
        </TableCell>
      </TableRow>
      <>
        {isOpen && (
          <TableRow
            sx={{
              "&:last-child td, &:last-child th": {
                border: 0,
              },
            }}>
            <TableCell
              style={
                currentMode === "Dark"
                  ? {color: "white", borderBottom: "none"}
                  : {color: "black", borderBottom: "none"}
              }
              align="center"
              colSpan={6}>
              <div className={customCss.manualsContainer}>
                <Manuals
                  parent={useAt !== "Alarm SOP" ? "Maintenance" : useAt}
                  manual_id={data?._id}
                  alarm_sop_id={alarm_sop_id}
                  recData={data}
                  machineName={machineName}
                  name={data?.title}
                  userName={`${user?.fname} ${user?.lname}`}
                  moduleName={useAt !== "Alarm SOP" ? "Maintenance" : useAt}
                  onStepIndexChange={setCurrentStepIndex}
                />
              </div>
            </TableCell>
          </TableRow>
        )}
      </>

      {useAt !== "Alarm SOP" ? (
        <Dialog open={openDel}>
          <Delete onClose={() => setOpenDel(false)} onDelete={deleteData} />
        </Dialog>
      ) : (
        <HandleAlarmSop
          showDialog={openDel}
          useAt={"Delete"}
          machineId={data.mid}
          alarmSOPData={data}
          handleClose={() => setOpenDel(false)}
          handleDelete={deleteAlarmsSopData}
          handleSubmitSuccess={onAlarmSopEditOrDeleteSuccess}
        />
      )}

      {useAt === "Alarm SOP" ? (
        <HandleAlarmSop
          showDialog={openEdit}
          useAt={"Update"}
          machineId={data.mid}
          alarmSOPData={data}
          handleClose={() => setOpenEdit(false)}
          handleSubmitSuccess={onAlarmSopEditOrDeleteSuccess}
        />
      ) : (
        <Dialog open={openEdit} fullWidth>
          <DialogTitle
            style={
              currentMode === "Dark"
                ? {backgroundColor: themeColors.dark.secondary}
                : {backgroundColor: "white"}
            }>
            Edit Maintenance - [{data?.title}]
          </DialogTitle>
          <DialogContent
            style={
              currentMode === "Dark"
                ? {backgroundColor: themeColors.dark.secondary}
                : {backgroundColor: "white"}
            }>
            <EditMaintenance
              handleClose={() => setOpenEdit(false)}
              mid={data.mid}
              data={data}
              machineName={machineName}
              MaintenanceTitle={data?.title}
              userName={`${user?.fname} ${user?.lname}`}
            />
          </DialogContent>
        </Dialog>
      )}

      <Dialog open={openAssignIssue} fullWidth>
        <DialogTitle
          style={
            currentMode === "Dark"
              ? {backgroundColor: "#212B36", color: "white"}
              : {}
          }>
          Assign Issue Module - [{data?.title}]
        </DialogTitle>
        <DialogContent
          style={
            currentMode === "Dark"
              ? {backgroundColor: "#212B36", color: "white"}
              : {}
          }>
          {Array.isArray(issues) &&
            issues !== undefined &&
            issues !== null &&
            issues.map(dataMap =>
              dataMap.id == data.issue_id ? (
                <>
                  {" "}
                  <b>Active:</b>
                  {data.issue_id && issueRedirect()}
                </>
              ) : null,
            )}
          <Select
            variant="outlined"
            fullWidth
            value={issueId}
            onChange={e => onIssueSelection(e)}
            style={{
              marginBottom: "24px",
            }}>
            <MenuItem
              style={
                currentMode === "Dark"
                  ? {backgroundColor: "#212B36", color: "white"}
                  : {}
              }
              value="newIssue">
              <span className="text-red-500 animate-pulse">Create New </span>
            </MenuItem>
            {Array.isArray(issues) &&
              issues !== undefined &&
              issues !== null &&
              issues.map(issue => (
                <MenuItem
                  key={issue._id}
                  style={
                    currentMode === "Dark"
                      ? {backgroundColor: "#212B36", color: "white"}
                      : {}
                  }
                  value={issue._id}>
                  <span className="capitalize"> {issue.name} </span>
                </MenuItem>
              ))}
          </Select>

          <DialogActions
            style={
              currentMode === "Dark"
                ? {backgroundColor: "#212B36", color: "white"}
                : {}
            }>
            <Button
              onClick={() => setOpenAssignIssue(false)}
              variant="outlined">
              Cancel
            </Button>
            <Button
              onClick={saveIssueId}
              variant="contained"
              startIcon={<BugReportIcon />}
              disabled={changeBool}>
              Save Issue
            </Button>
          </DialogActions>
        </DialogContent>
      </Dialog>

      <AddStepDialog
        open={open}
        setOpen={setOpen}
        data={data?.mid}
        machinetype={data?.type}
        manual_id={manual_id}
        alarm_sop_id={alarm_sop_id}
        machineName={machineName}
        steps={steps}
        setRefreshCount={setRefreshCount}
        refreshCount={refreshCount}
        currentMode={currentMode}
        parent={parent}
        handleHastageData={handleHastageData}
        setTempHashes={setTempHashes}
        hashArray={hashArray}
        temptemp={temptemp}
        currentStepIndex={currentStepIndex} // Pass current step index to AddStepDialog
      />

      <ReportDialog
        open={openReportConfirm}
        onClose={() => setOpenReportConfirm(false)}
        onConfirm={handleGenerateReport}
        title={data.title}
      />

      <SopPreviewDialog
        open={openSop}
        onClose={() => setOpenSop(false)}
        sopUrl={data?.sop_url}
        title={data?.title}
        currentMode={currentMode}
      />

      <ArrangeStepsDialog
        open={openArrangeSteps}
        onClose={() => setOpenArrangeSteps(false)}
        steps={data?.steps}
        currentMode={currentMode}
        maintenanceId={data?._id}
        useAt={useAt}
      />
    </>
  );
};

export default MaintenanceItem;
