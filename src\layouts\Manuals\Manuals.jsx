import {CloseCircleOutlined} from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  DialogActions,
  Autocomplete,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import Box from "@mui/material/Box";
import {DropzoneArea} from "material-ui-dropzone";
//import Dropzone from 'react-dropzone'
import React, {useContext, useEffect, useRef, useState} from "react";
// import { useParams } from 'react-router-dom';
import Carousel from "react-material-ui-carousel";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";
import Paper from "@mui/material/Paper";
import AddIcon from "@mui/icons-material/Add";
import ManualItem from "./ManualItem";
import {db} from "../../firebase";
import {firebaseLooper} from "../../tools/tool";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import {useStorage} from "../../hooks/useStorage";
import {companies, companyId_constant, liveEvents} from "../../constants/data";
import RemoveIcon from "@mui/icons-material/Remove";
import "./Manual.scss";
import LoggingFunction from "../../components/CFR-Report/LoggingFunction";
import ModalDiv from "./ModalDiv";
import {ButtonBasic} from "../../components/buttons/Buttons";
import {useStateContext} from "../../context/ContextProvider";
import {DeleteByUrl} from "../../utils/StorageOptions";
import {useParams, useNavigate} from "react-router-dom";
import {themeColors} from "../../infrastructure/theme";
import axios from "axios";
import {dbConfig} from "../../infrastructure/db/db-config";
import {convertBase64} from "../../hooks/useBase64";
import {useMongoRefresh} from "../../services/mongo-refresh.context";
import {
  useMaintenanceInfo,
  useMaintenanceInfoSeter,
} from "../../context/MaintenanceContext";
import {HashtagContext} from "../../services2/hashtag/hashtag.context";
import {sharedCss} from "../../styles/sharedCss";
import {makeStyles} from "@mui/styles";
import {useAuth} from "../../hooks/AuthProvider";
import {useCreateMachineCfr} from "../../hooks/cfr/machineCfrProvider";
import DeleteIcon from "@mui/icons-material/Delete";
import {useHashtagsSetter} from "../../services3/hashtag/hashtagcontext";
import {VscDebugStepOver} from "react-icons/vsc";
import NoDataComponent from "../../components/commons/noData.component";
import NotAccessible from "../../components/not-accessible/not-accessible";
import {useCheckAccess} from "../../utils/useCheckAccess";

const useCustomStyles = makeStyles(theme => ({
  manulasConatiner: {
    backgroundColor: theme.palette.custom.backgroundForth,
    color: theme.palette.custom.textColor,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    minHeight: "48vh",
    // padding: "0.5rem",
  },
}));

const scrollToRef = ref => window.scrollTo(0, ref.current.offsetTop);

const Manuals = ({
  recData,
  manual_id,
  alarm_sop_id,
  machineName,
  moduleName,
  name,
  userName,
  parent,
  onStepIndexChange, // add this prop
  closeAutoExpandedStepDataIfNotPresent = () => {},
}) => {
  const [steps, setSteps] = useState([]);
  const [loadingSteps, setLoadingSteps] = useState(true);
  const [sensorList, setSensorList] = useState([]); // liveData2 aaray
  const [maintenance, setMaintenance] = useState({});
  // const [openForModal, setOpenForModal] = useState(false);
  const {currentColor, currentMode, currentColorLight} = useStateContext();
  const {refreshCount, setRefreshCount} = useMongoRefresh();
  const [stepindex, setStepindex] = useState(0);
  const maintinfocontext = useMaintenanceInfo();
  const customCss = useCustomStyles();
  const {hashes, handleHastageData, setTempHashes, hashArray, temptemp} =
    useContext(HashtagContext);

  const getAllSteps = async () => {
    try {
      setLoadingSteps(true);
      const fullUrl =
        parent !== "Alarm SOP"
          ? `${dbConfig.url}/stepdata/getManualSteps/${manual_id}`
          : `${dbConfig.url}/alarmSopStepData/getAlarmSOPSteps/${alarm_sop_id}`;
      const response = await axios.get(fullUrl);
      // Sort by sortKey instead of index
      const sortedSteps = (response?.data?.data || []).sort(
        (a, b) => a.sortKey - b.sortKey,
      );

      setSteps(sortedSteps);
      // Adjust stepindex to ensure it's valid after fetching new steps
      setStepindex(prevIndex => {
        if (sortedSteps.length === 0) {
          return 0;
        }
        // If maintinfocontext is set, prioritize hashtag redirection
        if (maintinfocontext?.step_id) {
          const stepIndex = sortedSteps.findIndex(
            step => step._id === maintinfocontext.step_id,
          );
          if (stepIndex !== -1) {
            return stepIndex;
          }
        }
        // Cap the index to the valid range, ensuring non-negative
        const newIndex = Math.max(
          0,
          Math.min(prevIndex, sortedSteps.length - 1),
        );
        return newIndex;
      });
    } catch (error) {
      console.error("Error fetching steps:", error);
    } finally {
      setLoadingSteps(false);
    }
  };

  const hashtagfunction = useHashtagsSetter();
  const endpointForAccessCheck =
    parent !== "Alarm SOP" ? "stepdata" : "alarmSopStepData";
  const hasStepGETAccess = useCheckAccess(endpointForAccessCheck, "GET");

  const getMaintenanceData = async () => {
    let endpoint;
    if (parent === "Maintenance") {
      endpoint = `${dbConfig.url}/maintenance/${manual_id}`;
    } else if (parent === "ChangeOver") {
      endpoint = `${dbConfig.url}/changeOver/${manual_id}`;
    } else if (parent !== "Alarm SOP") {
      endpoint = `${dbConfig.url}/training/${manual_id}`;
    } else {
      endpoint = `${dbConfig.url}/alarmSopStepData/${alarm_sop_id}`;
    }
    try {
      const response = await axios.get(endpoint);
      setMaintenance(response?.data?.data);
    } catch (error) {
      console.error("Error fetching maintenance data:", error);
    }
  };

  const getSensorData = async () => {
    try {
      const response = await axios.get(
        `${dbConfig.url}/liveData/getFromMachine/${recData?.mid}`,
      );
      setSensorList(response?.data?.data);
    } catch (error) {
      console.error("Error fetching sensor data:", error);
    }
  };

  useEffect(() => {
    const loadStepsAndData = async () => {
      await Promise.all([getAllSteps(), getMaintenanceData(), getSensorData()]);
    };
    loadStepsAndData();
  }, [refreshCount, manual_id, maintinfocontext, hashes?.length]);

  // Notify parent when stepindex changes
  useEffect(() => {
    if (onStepIndexChange) {
      onStepIndexChange(stepindex);
    }
  }, [stepindex, onStepIndexChange]);

  console.log("maintenance:", maintenance, steps);

  const filterManualData = data => {
    if (parent !== "Alarm SOP") {
      return data.manual_id === manual_id;
    }

    return data.alarm_sop_id === alarm_sop_id;
  };

  return (
    <div
      className={`ManualDataSection ${customCss.manulasConatiner}`}
      component={Paper}>
      {/* <div className="ManualDataContainer ">
        <div className="manualDataHeading">
          <div className="steps px-2">Steps</div>
          <div className="flex">
            <div className="px-2 py-1">
              <Tooltip title="Add Step" placement="top">
                <Button variant="contained" size="small" onClick={() => setOpen(true)}>
                  <VscDebugStepOver />
                </Button>
              </Tooltip>
            </div>
          </div>
        </div>
      </div> */}
      <div className="carouselContainer">
        {/* {console.log("Stepindex carousel ke andar",stepindex)} */}
        {JSON.stringify(maintenance) !== "{}" && steps?.length ? (
          <Carousel
            key={steps.length}
            index={stepindex}
            NextIcon={<NavigateNextIcon />}
            PrevIcon={<NavigateBeforeIcon />}
            fullHeightHover={false}
            cycleNavigation={false}
            navButtonsProps={{
              style: {
                backgroundColor: "#666",
                color: "#fff",
                borderRadius: "50%",
              },
            }}
            indicators={false}
            autoPlay={false}
            animation={steps.length === 2 ? "fade" : "slide"}
            duration="500"
            navButtonsAlwaysVisible={true}
            style={currentMode === "Dark" ? {background: "#161C24"} : {}}
            className="carousel"
            onChange={setStepindex} // update stepindex on carousel change
          >
            {steps?.filter(filterManualData)?.map((data, index) => (
              <ManualItem
                key={data._id}
                stepKey={index}
                step={data}
                steps={steps} // Pass the full steps array
                machineName={machineName}
                name={name}
                moduleName={moduleName}
                userName={userName}
                mType={maintenance?.type}
                parent={parent}
                sensorList={sensorList}
                manual_id={parent !== "Alarm SOP" ? manual_id : alarm_sop_id}
                maintenance={maintenance}
                setStepindex={setStepindex}
                stepindex={stepindex}
              />
            ))}
          </Carousel>
        ) : loadingSteps ? (
          <CircularProgress />
        ) : hasStepGETAccess ? (
          <NoDataComponent
            useAtTable={false}
            noDataMessage={"No Steps available"}
          />
        ) : (
          <NotAccessible />
        )}
      </div>
    </div>
  );
};

export default Manuals;
