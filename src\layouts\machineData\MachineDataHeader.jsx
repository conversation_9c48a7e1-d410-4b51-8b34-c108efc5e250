import React, { useEffect, useState } from "react";
import { NavLink, useParams } from "react-router-dom";
import { useAuth } from "../../hooks/AuthProvider";
import { useStateContext } from "../../context/ContextProvider";
import { Box, Tab, Tabs } from "@mui/material";
import { makeStyles } from "@mui/styles";
import { useLocation } from "react-router-dom";

const useStyles = makeStyles((theme) => ({
  tabContainer: {
    marginBottom: "0.5rem",
    overflowX: "auto", // Add horizontal scrollbar
  },
  selected: {
    color: theme.palette.primary.main,
    fontWeight: "bold",
  },
  notSelected: {
    color: theme.palette.custom.textColor,
    fontWeight: "bold",
  },
  tab: {
    minWidth: "120px", // Adjust the minimum width of each tab
  },
}));

const MachineDataHeader = () => {
  const { mid } = useParams();
  const [machineData, setMachineData] = useState([]);
  const { currentUser } = useAuth();
  const [disableMaintenance, setDisableMaintenance] = useState(null);
  const [disableFAT, setDisableFAT] = useState(null);
  const { currentColor, currentMode, currentColorLight } = useStateContext();

  const classes = useStyles();
  const [value, setValue] = useState(0);
  const location = useLocation();

  useEffect(() => {
    const currentPath = location.pathname;
    const pathPrefix = currentPath.substring(0, currentPath.lastIndexOf("/"));

    switch (pathPrefix) {
      case "/annotation":
        setValue(0);
        break;
      case "/training":
        setValue(1);
        break;
      case "/changeOver":
        setValue(2);
        break;
      case "/maintenance":
        setValue(3);
        break;
      case "/calibration":
        setValue(4);
        break;
      case "/FAT":
        setValue(5);
        break;
      case "/SAT":
        setValue(6);
        break;
      case "/alarmsnew":
        setValue(7);
        break;
      case "/OEE":
        setValue(8);
        break;
      case "/dprnew":
        setValue(9);
        break;
      case "/forecast":
        setValue(10);
        break;
      case "/gemba":
        setValue(11);
        break;
      case "/lineClearance":
        setValue(12);
        break;
      default:
        break;
    }
  }, [location]);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <Box className={classes.tabContainer}>
      <Tabs
        value={value}
        onChange={handleChange}
        aria-label="basic tabs example"
        variant="scrollable" // Enable scrolling
        scrollButtons="auto" // Show scroll buttons automatically
      >
        <Tab
          label="Live data"
          value={0}
          exact
          component={NavLink}
          to={`/annotation/${mid}`}
          className={`${classes.notSelected} ${classes.tab}`}
          activeClassName={classes.selected}
        />
        <Tab
          label="Training"
          value={1}
          exact
          component={NavLink}
          to={`/training/${mid}`}
          className={`${classes.notSelected} ${classes.tab}`}
          activeClassName={classes.selected}
        />
        <Tab
          label="Change Over"
          value={2}
          exact
          component={NavLink}
          to={`/changeOver/${mid}`}
          className={`${classes.notSelected} ${classes.tab}`}
          activeClassName={classes.selected}
        />
        {!disableMaintenance && (
          <Tab
            label="Maintenance"
            value={3}
            component={NavLink}
            to={`/maintenance/${mid}`}
            className={`${classes.notSelected} ${classes.tab}`}
            activeClassName={classes.selected}
          />
        )}
        {!disableMaintenance && (
          <Tab
            label="Calibration"
            value={4}
            component={NavLink}
            to={`/calibration/${mid}`}
            className={`${classes.notSelected} ${classes.tab}`}
            activeClassName={classes.selected}
          />
        )}
        {!disableFAT && (
          <Tab
            label="AUDIT"
            className={`${classes.notSelected} ${classes.tab}`}
            activeClassName={classes.selected}
            value={5}
            exact
            component={NavLink}
            to={`/FAT/${mid}`}
          />
        )}
        <Tab
          label="Alarms"
          value={7}
          exact
          className={`${classes.notSelected} ${classes.tab}`}
          activeClassName={classes.selected}
          component={NavLink}
          to={`/alarmsnew/${mid}`}
        />
        <Tab
          label="OEE"
          value={8}
          exact
          className={`${classes.notSelected} ${classes.tab}`}
          activeClassName={classes.selected}
          component={NavLink}
          to={`/OEE/${mid}`}
        />
        <Tab
          label="DPR"
          value={9}
          exact
          className={`${classes.notSelected} ${classes.tab}`}
          activeClassName={classes.selected}
          component={NavLink}
          to={`/dprnew/${mid}`}
        />
        <Tab
          label="Predictive Maintenance"
          value={10}
          exact
          component={NavLink}
          to={`/forecast/${mid}`}
          className={`${classes.notSelected} ${classes.tab}`}
          activeClassName={classes.selected}
        />
        <Tab
          label="Gemba"
          value={11}
          exact
          component={NavLink}
          to={`/gemba/${mid}`}
          className={`${classes.notSelected} ${classes.tab}`}
          activeClassName={classes.selected}
        />
        <Tab
          label="Line Clearance"
          value={12}
          exact
          component={NavLink}
          to={`/lineClearance/${mid}`}
          className={`${classes.notSelected} ${classes.tab}`}
          activeClassName={classes.selected}
        />
      </Tabs>
    </Box>
  );
};

export default MachineDataHeader;
