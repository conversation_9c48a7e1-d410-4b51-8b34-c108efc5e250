import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import moment from "moment";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
  Button,
  TablePagination,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TableHead,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import DPRForm from "./DPRForm";
import ColumnSelectionModal from "./ColumnSelectionModal";
import AdvancedFiltersModal from "./AdvancedFiltersModal";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { sharedCss } from "../../styles/sharedCss";
import { useStateContext } from "../../context/ContextProvider";
import { useCommonOuterContainerStyle } from "../../styles/useCommonOuterContainerStyle";
import { commonRowStyle } from "../../layouts/machineData/MaintenanceReportDataMain";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { useCheckAccess } from "../../utils/useCheckAccess";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import jsPDF from "jspdf";
import "jspdf-autotable";
import MachineDataHeader from "../../layouts/machineData/MachineDataHeader";

// Backend model field-to-label mapping
const fieldLabels = {
  start_date: "Start Date",
  shift: "Shift",
  line: "Line",
  product_name: "Product Name",
  batch_number: "Batch Number",
  pack_style: "Pack Style",
  running_rpm: "Running RPM",
  total_shift_time: "Total Shift Time",
  actual_run_hours: "Actual Run Hours",
  total_downtime: "Total Downtime",
  target: "Target (Do not Edit)",
  start_shipper_nos: "Start Shipper (Nos)",
  end_shipper_nos: "End Shipper (Nos)",
  qty_per_shipper: "QTY/SHIPPER",
  actual_minutes: "Actual Minutes (Do not Edit)",
  reason: "Reason (Write in Detail)",
  operator: "Operator",
  executive: "Executive",
  incharge: "Incharge",
  casual_manpower: "Casual Manpower",
  shift_instance: "Shift Instance",
  concatenate: "Concatenate",
  optimal_speed: "Optimal Speed (For OEE Calculation)",
  output_as_per_optimal_speed: "Output as per Optimal Speed",
  availability: "Availability",
  performance: "Performance",
  quality: "Quality",
  oee_percentage: "OEE%",
};

// Modified TableHeader Component to Support Sorting
const TableHeader = ({ columns, currentMode, handleSort, sortConfig }) => {
  const headerStyle =
    currentMode === "Dark"
      ? { backgroundColor: "#212B36", color: "white" }
      : { backgroundColor: "#E0E0E0", color: "black" };

  return (
    <TableHead>
      <TableRow>
        {columns.map((column, index) => (
          <TableCell
            key={index}
            align={column.align || "left"}
            sx={{
              width: column.width,
              height: "56px",
              minWidth: column.minWidth || "120px",
              position: column.key === "actions" ? "sticky" : "relative",
              right: column.key === "actions" ? 0 : undefined,
              backgroundColor:
                column.key === "actions"
                  ? headerStyle.backgroundColor
                  : undefined,
              zIndex: column.key === "actions" ? 2 : 1,
            }}
            style={{ ...headerStyle, cursor: "pointer" }}
            onClick={() => column.key !== "actions" && handleSort(column.key)}
          >
            {column.label}
            {sortConfig.key === column.key &&
              (sortConfig.direction === "asc" ? " 🔼" : " 🔽")}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
};

const DPRComp = () => {
  const { mid } = useParams();
  const defaultVisibleColumns = [
    "start_date",
    "shift",
    "line",
    "product_name",
    "batch_number",
  ];
  const [visibleColumns, setVisibleColumns] = useState(defaultVisibleColumns);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAdvancedFilterOpen, setIsAdvancedFilterOpen] = useState(false);
  const [filters, setFilters] = useState({});
  const [sortConfig, setSortConfig] = useState({ key: null, direction: null });
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [rows, setRows] = useState([]);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingRow, setEditingRow] = useState(null);
  const [deleteRow, setDeleteRow] = useState(null);
  const [utilsData, setUtilsData] = useState({});
  const [dropdownOptions, setDropdownOptions] = useState({
    performer: [],
    planner: [],
    approver: [],
  });
  const [reportPeriod, setReportPeriod] = useState("day");
  const [selectedDate, setSelectedDate] = useState(
    moment().format("YYYY-MM-DD"),
  );

  const { currentColor, currentMode } = useStateContext() || {
    currentColor: "#1976d2",
    currentMode: "Light",
  };

  const useStyles = sharedCss();
  const sharedStyles = useStyles || {};
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  const hasDprGETAccess = useCheckAccess("dpr", "GET");
  const hasDprPOSTAccess = useCheckAccess("dpr", "POST");
  const hasDprPUTAccess = useCheckAccess("dpr", "PUT");
  const hasDprDELETEAccess = useCheckAccess("dpr", "DELETE");

  const handleColumnToggle = (column) => {
    setVisibleColumns((prev) =>
      prev.includes(column)
        ? prev.filter((col) => col !== column)
        : [...prev, column],
    );
  };

  const toggleModal = () => {
    setIsModalOpen((prev) => !prev);
  };

  const toggleAdvancedFilterModal = () => {
    setIsAdvancedFilterOpen((prev) => !prev);
  };

  const handleSort = (key) => {
    setSortConfig((prev) => {
      if (prev.key === key) {
        return { key, direction: prev.direction === "asc" ? "desc" : "asc" };
      }
      return { key, direction: "asc" };
    });
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleCreate = () => {
    setEditingRow(null);
    setIsFormOpen(true);
  };

  const handleEdit = async (row) => {
    console.log("Row data:", row); // Check if machineId is present
    try {
      console.log("Editing row:", row._id);
      const response = await axios.get(
        `${dbConfig.url}/losstreereports/${row._id}`,
      );

      const lossTreeReasons = response.data?.data || [];
      const rowDataWithReasons = {
        ...row,
        start_date: row.start_date
          ? moment(row.start_date).format("YYYY-MM-DD")
          : "",
        reasons: lossTreeReasons,
      };

      console.log("Editing row with reasons:", rowDataWithReasons);
      setEditingRow(rowDataWithReasons);
      setIsFormOpen(true);
    } catch (error) {
      console.error("Error fetching loss tree reasons:", error);
      setEditingRow({
        ...row,
        start_date: row.start_date
          ? moment(row.start_date).format("YYYY-MM-DD")
          : "",
        reasons: [],
      });
      setIsFormOpen(true);
    }
  };

  const handleDelete = (row) => {
    if (row && row._id) {
      setDeleteRow(row);
    } else {
      console.error("Row does not have a valid _id:", row);
    }
  };

  const confirmDelete = async () => {
    try {
      if (deleteRow && deleteRow._id) {
        await axios.delete(`${dbConfig.url}/dpr/${deleteRow._id}`);
        toastMessageSuccess({ message: "Row deleted successfully" });
        setRows((prev) => prev.filter((r) => r._id !== deleteRow._id));
        setDeleteRow(null);
      } else {
        console.error(
          "Cannot delete row: deleteRow or deleteRow._id is undefined",
        );
      }
    } catch (error) {
      console.error("Error deleting row:", error);
    }
  };

  const fetchUtilsData = async () => {
    try {
      const [linesData, shiftData, productData, reasonsData, usersData, allLines, machineData, machineTitle] =
        await Promise.all([
          mid
            ? axios.get(`${dbConfig.url}/machines/getFromMachineId/${mid}`)
            : axios.get(`${dbConfig.url}/lines`),
          axios.get(`${dbConfig.url}/shifts`),
          mid
            ? axios.get(`${dbConfig.url}/products/getFromMachine/${mid}`)
            : axios.get(`${dbConfig.url}/products`),
          axios.get(`${dbConfig.url}/losstreereasons`),
          axios.get(`${dbConfig.url}/users`),
          axios.get(`${dbConfig.url}/lines`),
          mid 
          ? axios.get(`${dbConfig.url}/machines/getFromMachineId/${mid}`)
          : axios.get(`${dbConfig.url}/machines`),
          axios.get(`${dbConfig.url}/machines`)
        ]);

      const users = usersData.data?.data || [];
      const operators = users
        .filter((user) => user.role === 2)
        .map((user) => user.email.split("@")[0]);
      const executives = users
        .filter((user) => user.role === 3)
        .map((user) => user.email.split("@")[0]);
      const incharges = users
        .filter((user) => user.role === 1)
        .map((user) => user.email.split("@")[0]);

      setDropdownOptions({
        performer: operators,
        planner: executives,
        approver: incharges,
      });

      setUtilsData({
        lineData: linesData.data?.data,
        shiftData: shiftData.data?.data,
        productData: productData.data?.data,
        reasonsData: reasonsData.data?.data,
        lineTitle: allLines?.data?.data,
        machineData: machineData.data?.data,
        machineTitle: machineTitle.data?.data,
      });
    } catch (error) {
      console.error("Error fetching utils data:", error);
    }
  };

  const fetchRowsData = async () => {
    try {
      const endpoint = mid
        ? `${dbConfig.url}/dpr/getFromMachine/${mid}`
        : `${dbConfig.url}/dpr`;
      const response = await axios.get(endpoint);
      const fetchedData = response.data?.data || [];
      setRows(fetchedData);
    } catch (error) {
      console.error("Error fetching rows data:", error);
    }
  };

  useEffect(() => {
    fetchRowsData();
    fetchUtilsData();
  }, [mid]);

  const handleFormSubmit = async (data) => {
    try {
      let response;
      if (editingRow) {
        response = await axios.put(
          `${dbConfig.url}/dpr/${editingRow._id}`,
          data,
        );
        const updatedData = response.data?.data || response.data;
        toastMessageSuccess({ message: "Data updated successfully" });
        setRows((prev) =>
          prev.map((row) => (row._id === editingRow._id ? updatedData : row)),
        );
      } else {
        response = await axios.post(`${dbConfig.url}/dpr`, data);
        const newData = response.data?.data || response.data;
        toastMessageSuccess({ message: "Data created successfully" });
        setRows((prev) => [...prev, newData]);
      }
      await fetchRowsData();
      setIsFormOpen(false);
      setEditingRow(null);
      return response.data;
    } catch (error) {
      console.error(
        "Error submitting form data:",
        error.response?.data || error.message,
      );
      throw error;
    }
  };

  const applyColumnSelection = () => {
    toggleModal();
  };

  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const applyFilters = () => {
    toggleAdvancedFilterModal();
  };

  const calculateOEE = () => {
    const filteredRows = rows.filter((row) =>
      reportPeriod === "day"
        ? moment(row.start_date).isSame(moment(selectedDate), "day")
        : moment(row.start_date).isSame(moment(selectedDate), "month"),
    );

    const validOEEValues = filteredRows
      .map((row) => row.oee_percentage)
      .filter((value) => typeof value === "number" && !isNaN(value));

    const total = validOEEValues.reduce((sum, value) => sum + value, 0);
    const count = validOEEValues.length;

    const averageOEE = count > 0 ? (total / count).toFixed(2) : 0;

    return { averageOEE, count };
  };

  const generatePDF = () => {
    const doc = new jsPDF();
    const { averageOEE, count } = calculateOEE();
    const periodText =
      reportPeriod === "day"
        ? `Day: ${moment(selectedDate).format("DD/MM/YYYY")}`
        : `Month: ${moment(selectedDate).format("MMMM YYYY")}`;

    doc.setFontSize(18);
    doc.text("OEE Report", 14, 22);
    doc.setFontSize(12);
    doc.text(`Period: ${periodText}`, 14, 32);
    doc.text(`Average OEE: ${averageOEE}%`, 14, 42);
    doc.text(`Total Records: ${count}`, 14, 52);

    const tableColumns = [
      ...visibleColumns.map((col) => fieldLabels[col]),
      fieldLabels["oee_percentage"],
    ];
    const tableRows = rows
      .filter((row) =>
        reportPeriod === "day"
          ? moment(row.start_date).isSame(moment(selectedDate), "day")
          : moment(row.start_date).isSame(moment(selectedDate), "month"),
      )
      .map((row) => [
        ...visibleColumns.map((col) =>
          col === "start_date" ? formatDate(row[col]) : row[col] || "",
        ),
        row.oee_percentage ? `${row.oee_percentage.toFixed(2)}%` : "",
      ]);

    doc.autoTable({
      head: [tableColumns],
      body: tableRows,
      startY: 62,
      theme: "grid",
      headStyles: { fillColor: [33, 150, 243], textColor: [255, 255, 255] },
      styles: { cellPadding: 2, fontSize: 10 },
    });

    doc.save(
      `OEE_Report_${reportPeriod}_${moment(selectedDate).format(
        "YYYYMMDD",
      )}.pdf`,
    );
  };

  const filteredData = rows.filter((row) =>
    Object.entries(filters).every(([key, value]) =>
      row[key]?.toString().toLowerCase().includes(value.toLowerCase()),
    ),
  );

  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortConfig.key) return 0;
    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];
    if (sortConfig.direction === "asc") {
      return aValue > bValue ? 1 : -1;
    }
    return aValue < bValue ? -1 : 1;
  });

  const paginatedData = sortedData.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage,
  );

  const formatDate = (dateString) => {
    if (!dateString) return "";
    return moment(dateString).format("DD/MM/YYYY");
  };

  const columns = [
    ...visibleColumns.map((column) => ({
      key: column,
      label: fieldLabels[column],
      align: "left",
      width: "auto",
      minWidth: "120px",
    })),
    {
      key: "actions",
      label: "Actions",
      align: "center",
      width: "150px",
      minWidth: "150px",
    },
  ];

  return (
    <section>
      {mid && <MachineDataHeader />}
      <div
        className={`${sharedStyles.sectionContainer} ${sharedStyles.backgroundLight} border-radius-outer`}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginBottom: "1rem",
            marginTop: "1rem",
          }}
        >
          <Button
            variant="contained"
            color="primary"
            size="medium"
            onClick={handleCreate}
            disabled={!hasDprPOSTAccess}
            sx={{
              textTransform: "none",
              fontWeight: "bold",
              fontSize: "0.75rem",
              padding: "4px 8px",
              lineHeight: "1.2",
              minWidth: "80px",
            }}
          >
            Create
          </Button>
          <div style={{ display: "flex", gap: "0.75rem" }}>
            <Button
              variant="contained"
              color="primary"
              size="medium"
              onClick={toggleAdvancedFilterModal}
              disabled={!hasDprGETAccess}
              sx={{
                textTransform: "none",
                fontWeight: "bold",
                fontSize: "0.75rem",
                padding: "8px 12px",
                lineHeight: "1.2",
                minWidth: "80px",
              }}
            >
              Advance Filters
            </Button>
            <Button
              variant="contained"
              color="primary"
              size="medium"
              onClick={toggleModal}
              disabled={!hasDprGETAccess}
              sx={{
                textTransform: "none",
                fontWeight: "bold",
                fontSize: "0.75rem",
                padding: "4px 8px",
                lineHeight: "1.2",
                minWidth: "80px",
              }}
            >
              Select Columns
            </Button>
            <FormControl sx={{ minWidth: 100 }}>
              <InputLabel sx={{ fontSize: "0.75rem" }}>Period</InputLabel>
              <Select
                value={reportPeriod}
                onChange={(e) => setReportPeriod(e.target.value)}
                label="Period"
                size="medium"
                sx={{
                  fontSize: "0.75rem",
                  height: "34px",
                  "& .MuiSelect-select": { padding: "4px 24px 4px 8px" },
                }}
              >
                <MenuItem
                  value="day"
                  sx={{ fontSize: "0.75rem", height: "34px" }}
                >
                  Day
                </MenuItem>
                <MenuItem
                  value="month"
                  sx={{ fontSize: "0.75rem", height: "34px" }}
                >
                  Month
                </MenuItem>
              </Select>
            </FormControl>
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              style={{
                padding: "4px",
                borderRadius: "4px",
                border: "1px solid #ccc",
                fontSize: "0.75rem",
                height: "34px",
                boxSizing: "border-box",
              }}
            />
            <Button
              variant="contained"
              color="primary"
              size="medium"
              onClick={generatePDF}
              disabled={!hasDprGETAccess}
              sx={{
                textTransform: "none",
                fontWeight: "bold",
                fontSize: "0.75rem",
                padding: "4px 8px",
                lineHeight: "1.2",
                minWidth: "80px",
              }}
            >
              Generate Report
            </Button>
          </div>
        </div>

        <div style={{ overflowX: "auto", maxWidth: "100%" }}>
          <TableContainer
            component={Paper}
            className="table border-radius-inner"
            sx={{
              ...commonOuterContainerStyle,
              minWidth: `${visibleColumns.length * 120 + 150}px`,
            }}
          >
            <Table stickyHeader>
              <TableHeader
                columns={columns}
                currentMode={currentMode}
                handleSort={handleSort}
                sortConfig={sortConfig}
              />
              <TableBody>
                {paginatedData.length > 0 ? (
                  paginatedData.map((row, rowIndex) => (
                    <TableRow
                      key={rowIndex}
                      sx={{
                        ...commonRowStyle,
                        "&:hover": {
                          backgroundColor:
                            currentMode === "Dark" ? "#616161" : "#f5f5f5",
                          "& td": {
                            backgroundColor: "inherit",
                          },
                        },
                      }}
                    >
                      {visibleColumns.map((column) => (
                        <TableCell
                          key={column}
                          sx={{
                            minWidth: "120px",
                            backgroundColor: "inherit",
                          }}
                          style={
                            currentMode === "Dark"
                              ? {
                                  color: "white",
                                  borderBottom: "1px solid white",
                                }
                              : { borderBottom: "1px solid #e0e0e0" }
                          }
                        >
                          {column === "start_date"
                            ? formatDate(row[column])
                            : row[column]}
                        </TableCell>
                      ))}
                      <TableCell
                        align="center"
                        sx={{
                          minWidth: "150px",
                          position: "sticky",
                          right: 0,
                          backgroundColor: "inherit",
                          zIndex: 10,
                        }}
                        style={
                          currentMode === "Dark"
                            ? { color: "white", borderBottom: "1px solid white" }
                            : { borderBottom: "1px solid #e0e0e0" }
                        }
                      >
                        <IconButton
                          onClick={() => handleEdit(row)}
                          disabled={!hasDprPUTAccess}
                          sx={{
                            color: !hasDprPUTAccess
                              ? currentMode === "Dark"
                                ? "#757575"
                                : "#B0BEC5"
                              : currentMode === "Dark"
                                ? "#B0BEC5"
                                : currentColor,
                            cursor: "pointer",
                            "&:hover": {
                              color: !hasDprPUTAccess
                                ? currentMode === "Dark"
                                  ? "#757575"
                                  : "#B0BEC5"
                                : currentMode === "Dark"
                                  ? "#ffffff"
                                  : "#1976d2",
                            },
                          }}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          onClick={() => handleDelete(row)}
                          disabled={!hasDprDELETEAccess}
                          sx={{
                            cursor: "pointer",
                            color: !hasDprDELETEAccess ? "grey.500" : "red",
                            "&:hover": {
                              color: !hasDprDELETEAccess ? "grey.500" : "#d32f2f",
                            },
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={visibleColumns.length + 1} align="center">
                      <span
                        style={{
                          fontWeight: "bold",
                          color: "gray",
                          fontSize: "1.2rem",
                        }}
                      >
                        No Data
                      </span>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </div>

        <TablePagination
          component="div"
          count={filteredData.length}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />

        <ColumnSelectionModal
          isOpen={isModalOpen}
          toggleModal={toggleModal}
          config={Object.keys(fieldLabels).map((key) => ({
            key,
            label: fieldLabels[key],
          }))}
          visibleColumns={visibleColumns}
          handleColumnToggle={handleColumnToggle}
          applyColumnSelection={applyColumnSelection}
        />

        <AdvancedFiltersModal
          isOpen={isAdvancedFilterOpen}
          toggleModal={toggleAdvancedFilterModal}
          config={Object.keys(fieldLabels).map((key) => ({
            key,
            label: fieldLabels[key],
          }))}
          handleFilterChange={handleFilterChange}
          applyFilters={applyFilters}
        />

        <DPRForm
          open={isFormOpen}
          onClose={() => setIsFormOpen(false)}
          onSubmit={handleFormSubmit}
          utilsData={utilsData}
          initialData={editingRow}
          dropdownOptions={dropdownOptions}
          fieldLabels={fieldLabels}
          mid={mid}
        />

        <Dialog open={!!deleteRow} onClose={() => setDeleteRow(null)}>
          <DialogTitle>Confirm Delete</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete this row?
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteRow(null)} color="primary">
              Cancel
            </Button>
            <Button onClick={confirmDelete} color="error">
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    </section>
  );
};

export default DPRComp;