import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  TextField,
  Button,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Grid,
  Typography,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  TableContainer,
} from "@mui/material";
import { Add, Delete } from "@mui/icons-material";
import axios from "axios";
import {
  numberFields,
  autoFilledFields,
  dropdownKeys,
  sections,
} from "./DPRFields";
import { ButtonBasicCancel } from "../buttons/Buttons";
import { dbConfig } from "../../infrastructure/db/db-config";
import { toastMessageSuccess, toastMessage } from "../../tools/toast";
import { toast } from "react-toastify";
import { calculateTimeTaken, formatDateShort, fromHHMM, getAllowedDates, isValidDDMMYYYY, isValidHHMM, reasonColors, toDDMMYYYY, toHHMM, toTitleCase, toYYYYMMDD } from "../../utils/constants";
import { line } from "d3";

const DPRForm = ({
  open,
  onClose,
  onSubmit,
  initialData = {},
  dropdownOptions = {},
  utilsData = {},
  mid,
}) => {
  const {
    shiftData = [],
    lineData = [],
    productData = [],
    reasonsData = [],
    lineTitle,
    machineData = [],
    machineTitle 
  } = utilsData;
  console.log("utilsData", utilsData);
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});
  const [deletedReasonIds, setDeletedReasonIds] = useState([]);
  const [reasons, setReasons] = useState([
    {
      category: "",
      description: "",
      start_date: "",
      end_date: "",
      start_time: "",
      end_time: "",
      time_taken: "",
    },
  ]);

  useEffect(() => {
    const today = toDDMMYYYY(new Date());

    let lineInitail = '';
    if (Array.isArray(lineData)) {
      lineInitail = lineData.map((line) => line.title)
    } else if (lineData && typeof lineData === "object" && lineData.line) {
      const match = lineTitle?.find((line) => line._id === lineData.line);
      lineInitail = match?.title || '';
    }
    
    // Initialize formData with machine from mid prop
    setFormData((prev) => ({
      ...prev,
      start_date: initialData?.start_date
        ? toDDMMYYYY(initialData.start_date)
        : today,
      machine: mid || initialData?.machine || "", // Auto-populate machine
      line: lineInitail,
      // Only spread initialData fields if in edit mode (initialData has _id)
      ...(initialData?._id ? Object.fromEntries(
        Object.entries(initialData || {}).filter(
          ([key]) => key !== "start_date" && key !== "machine",
        ),
      ) : {}),
    }));

    // Only initialize reasons if they haven't been modified
    if (
      reasons.length === 1 &&
      !reasons[0].category &&
      !reasons[0].description
    ) {
      let initialDate = today;
      if (initialData?.start_date) {
        const parsedDate = toDDMMYYYY(initialData.start_date);
        initialDate = parsedDate || today;
      }

      // Initialize reasons
      if (initialData?.reasons?.length > 0) {
        const mappedReasons = initialData.reasons.map((reason) => {
          const startDate = reason.start_date
            ? toDDMMYYYY(reason.start_date) || initialDate
            : initialDate;
          const endDate = reason.end_date
            ? toDDMMYYYY(reason.end_date) || initialDate
            : initialDate;
          const startTime = reason.start_time
            ? typeof reason.start_time === "number"
              ? toHHMM(reason.start_time)
              : isValidHHMM(reason.start_time)
                ? reason.start_time
                : ""
            : "";
          const endTime = reason.end_time
            ? typeof reason.end_time === "number"
              ? toHHMM(reason.end_time)
              : isValidHHMM(reason.end_time)
                ? reason.end_time
                : ""
            : "";
          const timeTaken = reason.duration
            ? String(reason.duration)
            : calculateTimeTaken(startTime, endTime);

          return {
            _id: reason._id || reason.id,
            category: reason.category || "",
            description: reason.downtime_detail || "",
            start_date: startDate,
            end_date: endDate,
            start_time: startTime,
            end_time: endTime,
            time_taken: timeTaken,
          };
        });
        setReasons(mappedReasons);
      } else {
        setReasons([
          {
            category: "",
            description: "",
            start_date: initialDate,
            end_date: initialDate,
            start_time: "",
            end_time: "",
            time_taken: "",
          },
        ]);
      }
    }
  }, [initialData, mid]);

 const resetForm = () => {
    const today = toDDMMYYYY(new Date());
    // Fully reset formData to initial state, ignoring initialData
    setFormData({
      start_date: today,
      machine: mid || "",
    });
    setReasons([
      {
        category: "",
        description: "",
        start_date: today,
        end_date: today,
        start_time: "",
        end_time: "",
        time_taken: "",
      },
    ]);
    setErrors({});
    setDeletedReasonIds([]);
  };

  const toNumber = (value) => {
    if (typeof value === "string" && isValidHHMM(value)) {
      return fromHHMM(value);
    }
    return typeof value === "number" ? value : parseFloat(value) || 0;
  };

  // Calculate time_taken
  useEffect(() => {
    const startTime = formData.start_time;
    const endTime = formData.end_time;

    if (
      startTime &&
      endTime &&
      isValidHHMM(startTime) &&
      isValidHHMM(endTime)
    ) {
      const startMinutes = fromHHMM(startTime);
      const endMinutes = fromHHMM(endTime);

      if (endMinutes >= startMinutes) {
        const timeTakenMinutes = endMinutes - startMinutes;
        setFormData((prev) => ({ ...prev, time_taken: timeTakenMinutes }));
        setErrors((prev) => ({
          ...prev,
          time_taken: undefined,
          start_time: undefined,
          end_time: undefined,
        }));
      } else {
        setErrors((prev) => ({
          ...prev,
          end_time: "End time must be after start time",
        }));
      }
    }
  }, [formData.start_time, formData.end_time]);

  // Validate reasons against total_downtime
  useEffect(() => {
    const totalDowntime = formData.total_downtime
      ? isValidHHMM(formData.total_downtime)
        ? fromHHMM(formData.total_downtime)
        : toNumber(formData.total_downtime)
      : 0;
    const totalReasonTimeTaken = reasons.reduce(
      (sum, r) => sum + (r.time_taken ? Number(r.time_taken) : 0),
      0,
    );

    if (totalDowntime && totalReasonTimeTaken !== totalDowntime) {
      setErrors((prev) => ({
        ...prev,
        reasons: `Total time taken of reasons (${toHHMM(
          totalReasonTimeTaken,
        )}) must equal total downtime (${toHHMM(totalDowntime)})`,
      }));
    } else {
      setErrors((prev) => ({ ...prev, reasons: undefined }));
    }
  }, [reasons, formData.total_downtime]);

  // Auto-filled fields error clearing
  useEffect(() => {
    const clearAutoFilledErrors = () => {
      const newErrors = { ...errors };
      const autoCalculatedFields = [
        "production_units",
        "actual_minutes",
        "total_downtime",
        "target",
        "output_as_per_optimal_speed",
        "availability",
        "performance",
        "oee_percentage",
        "concatenate",
        "shift_instance",
        "time_taken",
        "machine", // Add machine to auto-filled fields
      ];

      autoCalculatedFields.forEach((field) => {
        if (formData[field] !== undefined && formData[field] !== "") {
          delete newErrors[field];
        }
      });

      if (formData.product_name) {
        [
          "pack_style",
          "running_rpm",
          "optimal_speed",
          "quality",
          "qty_per_shipper",
        ].forEach((field) => {
          if (formData[field] !== undefined && formData[field] !== "") {
            delete newErrors[field];
          }
        });
      }

      if (formData.shift && formData.total_shift_time) {
        delete newErrors.total_shift_time;
        delete newErrors.shift_instance;
      }

      setErrors(newErrors);
    };

    clearAutoFilledErrors();
  }, [formData]);

  // Auto-calculation logic
  useEffect(() => {
    const end = toNumber(formData.end_shipper_nos);
    const start = toNumber(formData.start_shipper_nos);
    const qty = toNumber(formData.qty_per_shipper);
    const pack = toNumber(formData.pack_style);
    const totalTime = toNumber(formData.total_shift_time);
    const runMinutes = toNumber(formData.actual_run_hrs);
    const rpm = toNumber(formData.running_rpm);
    const optimal = toNumber(formData.optimal_speed);
    const quality = toNumber(formData.quality);

    const updates = {};

    if (totalTime && runMinutes) {
      updates.total_downtime = parseFloat(
        Math.max(0, totalTime - runMinutes).toFixed(2),
      );
    }
    if (totalTime && rpm && pack) {
      updates.target = Math.round((rpm * totalTime * pack * 60) / 60);
    }

    if (
      end &&
      start &&
      qty &&
      pack &&
      totalTime &&
      runMinutes &&
      rpm &&
      optimal &&
      quality
    ) {
      updates.production_units = (end - start) * qty;
      updates.actual_minutes = Math.round(updates.production_units * pack);
      updates.output_as_per_optimal_speed = Math.round(
        (Math.max(rpm, optimal) * totalTime * pack * 60) / 60,
      );
      updates.availability =
        totalTime > 0 ? Math.round((runMinutes / totalTime) * 100) : 0;
      updates.performance =
        updates.output_as_per_optimal_speed > 0
          ? Math.round(
              (updates.actual_minutes / updates.output_as_per_optimal_speed) *
                100,
            )
          : 0;
      updates.oee_percentage = Math.round(
        (quality * updates.performance * updates.availability) / 10000,
      );
      updates.concatenate = `${formData.line || ""}/${(
        formData.batch_number || ""
      ).slice(0, 3)}/${formData.pack_style || ""}`;
    }

    if (Object.keys(updates).length > 0) {
      setFormData((prev) => ({ ...prev, ...updates }));
    }
  }, [
    formData.end_shipper_nos,
    formData.start_shipper_nos,
    formData.qty_per_shipper,
    formData.pack_style,
    formData.total_shift_time,
    formData.actual_run_hrs,
    formData.running_rpm,
    formData.optimal_speed,
    formData.quality,
    formData.line,
    formData.batch_number,
  ]);

  const handleChange = (key, value) => {
    const newErrors = { ...errors };

    if (
      [
        "total_shift_time",
        "actual_run_hrs",
        "total_downtime",
        "shift_instance",
        "start_time",
        "end_time",
      ].includes(key)
    ) {
      if (value && value !== "" && !isValidHHMM(value)) {
        newErrors[key] = "Please enter time in HH:MM format (e.g., 08:30)";
      } else {
        delete newErrors[key];
      }
    } else if (key === "start_date" && value && !isValidDDMMYYYY(value)) {
      newErrors[key] =
        "Please enter date in DD/MM/YYYY format (e.g., 02/05/2025)";
    } else {
      delete newErrors[key];
    }

    if (key === "product_name") {
      const prod = productData.find((p) => p.title === value);
      if (prod) {
        const {
          pack_style,
          running_rpm,
          optimal_speed,
          quality,
          qty_per_shipper,
        } = prod;
        setErrors((prev) => {
          const newErrors = { ...prev };
          [
            "pack_style",
            "running_rpm",
            "optimal_speed",
            "quality",
            "qty_per_shipper",
          ].forEach((field) => {
            delete newErrors[field];
          });
          return newErrors;
        });
        setFormData((prev) => ({
          ...prev,
          [key]: value,
          pack_style,
          running_rpm,
          optimal_speed,
          quality,
          qty_per_shipper,
        }));
        return;
      }
    }

    if (key === "shift") {
      const shift = shiftData.find((s) => s.title === value);
      if (shift) {
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors.total_shift_time;
          delete newErrors.shift_instance;
          return newErrors;
        });
        setFormData((prev) => ({
          ...prev,
          [key]: value,
          total_shift_time: shift.total_shift_time,
          shift_instance: shift.instance_time,
        }));
        return;
      }
    }

    if (key === "start_date") {
      const allowedDates = getAllowedDates();
      const isYesterday = value === allowedDates[0];
      const isToday = value === allowedDates[1];

      if (isYesterday || isToday) {
        const newReasons = reasons.map((reason) => ({
          ...reason,
          start_date: value,
          end_date: isToday ? value : reason.end_date || value,
        }));
        setReasons(newReasons);

        setErrors((prev) => {
          const newErrors = { ...prev };
          reasons.forEach((_, index) => {
            delete newErrors[`reason_start_date_${index}`];
            if (isToday) {
              delete newErrors[`reason_end_date_${index}`];
            }
          });
          return newErrors;
        });
      }
    }

    const convertedValue =
      [
        "total_shift_time",
        "actual_run_hrs",
        "total_downtime",
        "shift_instance",
        "start_time",
        "end_time",
      ].includes(key) &&
      value &&
      isValidHHMM(value)
        ? value
        : value;

    setFormData((prev) => ({ ...prev, [key]: convertedValue }));
    setErrors(newErrors);
  };

  const handleReasonChange = (index, key, value) => {
    console.log(`Before update - reasons[${index}]:`, reasons[index]);
    const newReasons = [...reasons];
    newReasons[index] = { ...newReasons[index], [key]: value };
    console.log(`After update - reasons[${index}]:`, newReasons[index]);

    if (key === "category") {
      const reason = reasonsData.find((r) => r.category === value);
      newReasons[index].description = reason ? reason.description : "";
    }

    if (key === "start_date" || key === "end_date") {
      if (value && !isValidDDMMYYYY(value)) {
        setErrors((prev) => ({
          ...prev,
          [`reason_${key}_${index}`]:
            "Please enter date in DD/MM/YYYY format (e.g., 02/05/2025)",
        }));
      } else {
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[`reason_${key}_${index}`];
          return newErrors;
        });
      }

      const batchStartDate = formData.start_date
        ? new Date(toYYYYMMDD(formData.start_date))
        : null;
      const reasonDate =
        value && isValidDDMMYYYY(value) ? new Date(toYYYYMMDD(value)) : null;

      if (batchStartDate && reasonDate) {
        const nextDay = new Date(batchStartDate);
        nextDay.setDate(batchStartDate.getDate() + 1);

        if (
          key === "start_date" &&
          reasonDate.getTime() !== batchStartDate.getTime() &&
          reasonDate.getTime() !== nextDay.getTime()
        ) {
          setErrors((prev) => ({
            ...prev,
            [`reason_start_date_${index}`]:
              "Reason start date must match batch start date or the next day",
          }));
        }
        if (
          key === "end_date" &&
          reasonDate.getTime() !== batchStartDate.getTime() &&
          reasonDate.getTime() !== nextDay.getTime()
        ) {
          setErrors((prev) => ({
            ...prev,
            [`reason_end_date_${index}`]:
              "Reason end date must match batch start date or the next day",
          }));
        }
      }
    }

    if (key === "start_time" || key === "end_time") {
      if (value && !isValidHHMM(value)) {
        setErrors((prev) => ({
          ...prev,
          [`reason_${key}_${index}`]:
            "Please enter time in HH:MM format (e.g., 00:30)",
        }));
      } else {
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[`reason_${key}_${index}`];
          return newErrors;
        });
      }

      const shift = shiftData.find((s) => s.title === formData.shift);
      if (shift && formData.start_date) {
        const shiftStartMinutes = shift.start_time
          ? isValidHHMM(shift.start_time)
            ? fromHHMM(shift.start_time)
            : toNumber(shift.start_time)
          : 0;
        const shiftEndMinutes = shift.end_time
          ? isValidHHMM(shift.end_time)
            ? fromHHMM(shift.end_time)
            : toNumber(shift.end_time)
          : 0;
        const reasonTimeMinutes =
          value && isValidHHMM(value) ? fromHHMM(value) : null;
        const reasonDate =
          key === "start_time"
            ? newReasons[index].start_date
            : newReasons[index][
                key === "start_time" ? "start_date" : "end_date"
              ];
        const batchStartDate = formData.start_date
          ? new Date(toYYYYMMDD(formData.start_date))
          : null;
        const nextDay = batchStartDate ? new Date(batchStartDate) : null;
        if (nextDay) nextDay.setDate(batchStartDate.getDate() + 1);

        if (
          reasonTimeMinutes !== null &&
          shiftStartMinutes !== null &&
          shiftEndMinutes !== null &&
          reasonDate &&
          batchStartDate
        ) {
          const isNextDay =
            reasonDate &&
            isValidDDMMYYYY(reasonDate) &&
            new Date(toYYYYMMDD(reasonDate)).getTime() === nextDay?.getTime();
          const isSameDay =
            reasonDate &&
            isValidDDMMYYYY(reasonDate) &&
            new Date(toYYYYMMDD(reasonDate)).getTime() ===
              batchStartDate.getTime();

          const isOvernightShift = shiftEndMinutes < shiftStartMinutes;
          if (isOvernightShift) {
            const batchStartDateShort = formatDateShort(batchStartDate);
            const nextDayShort = formatDateShort(nextDay);
            const shiftStartTime12 = toHHMM(shift.start_time);
            const shiftEndTime12 = toHHMM(shift.end_time);

            if (key === "start_time") {
              if (isSameDay && reasonTimeMinutes < shiftStartMinutes) {
                setErrors((prev) => ({
                  ...prev,
                  [`reason_start_time_${index}`]: `Must be between ${batchStartDateShort} (${shiftStartTime12}) to ${nextDayShort} (${shiftEndTime12})`,
                }));
              } else if (isNextDay && reasonTimeMinutes > shiftEndMinutes) {
                setErrors((prev) => ({
                  ...prev,
                  [`reason_start_time_${index}`]: `Must be between ${batchStartDateShort} (${shiftStartTime12}) to ${nextDayShort} (${shiftEndTime12})`,
                }));
              }
            }
            if (key === "end_time") {
              if (isSameDay && reasonTimeMinutes < shiftStartMinutes) {
                setErrors((prev) => ({
                  ...prev,
                  [`reason_end_time_${index}`]: `Must be between ${batchStartDateShort} (${shiftStartTime12}) to ${nextDayShort} (${shiftEndTime12})`,
                }));
              } else if (isNextDay && reasonTimeMinutes > shiftEndMinutes) {
                setErrors((prev) => ({
                  ...prev,
                  [`reason_end_time_${index}`]: `Must be between ${batchStartDateShort} (${shiftStartTime12}) to ${nextDayShort} (${shiftEndTime12})`,
                }));
              }
            }
          } else {
            if (
              key === "start_time" &&
              isSameDay &&
              (reasonTimeMinutes < shiftStartMinutes ||
                reasonTimeMinutes > shiftEndMinutes)
            ) {
              setErrors((prev) => ({
                ...prev,
                [`reason_start_time_${index}`]: `Must be between ${toHHMM(shiftStartMinutes)} and ${toHHMM(shiftEndMinutes)}`,
              }));
            }
            if (
              key === "end_time" &&
              isSameDay &&
              (reasonTimeMinutes < shiftStartMinutes ||
                reasonTimeMinutes > shiftEndMinutes)
            ) {
              setErrors((prev) => ({
                ...prev,
                [`reason_end_time_${index}`]: `Must be between ${toHHMM(shiftStartMinutes)} and ${toHHMM(shiftEndMinutes)}`,
              }));
            }
          }
        }
      }
    }

    if (
      key === "start_time" ||
      key === "end_time" ||
      key === "start_date" ||
      key === "end_date"
    ) {
      const startTime =
        key === "start_time" ? value : newReasons[index].start_time;
      const endTime = key === "end_time" ? value : newReasons[index].end_time;
      const startDate =
        key === "start_date" ? value : newReasons[index].start_date;
      const endDate = key === "end_date" ? value : newReasons[index].end_date;

      if (
        startTime &&
        endTime &&
        startDate &&
        endDate &&
        isValidHHMM(startTime) &&
        isValidHHMM(endTime) &&
        isValidDDMMYYYY(startDate) &&
        isValidDDMMYYYY(endDate)
      ) {
        const startDateTime = new Date(
          `${toYYYYMMDD(startDate)}T${startTime}:00`,
        );
        const endDateTime = new Date(`${toYYYYMMDD(endDate)}T${endTime}:00`);

        if (endDateTime >= startDateTime) {
          const timeTakenMinutes = Math.round(
            (endDateTime - startDateTime) / (1000 * 60),
          );
          newReasons[index].time_taken = timeTakenMinutes;
          setErrors((prev) => {
            const newErrors = { ...prev };
            delete newErrors[`reason_end_time_${index}`];
            delete newErrors[`reason_end_date_${index}`];
            return newErrors;
          });
        } else {
          setErrors((prev) => ({
            ...prev,
            [`reason_end_time_${index}`]:
              "End date and time must be after start date and time",
          }));
        }
      } else {
        newReasons[index].time_taken = "";
      }
    }

    setReasons(newReasons);
  };

  const addReason = () => {
    console.log("Adding new reason");
    const batchStartDate = formData.start_date || toDDMMYYYY(new Date());
    const isToday = batchStartDate === toDDMMYYYY(new Date());
    setReasons([
      ...reasons,
      {
        category: "",
        description: "",
        start_date: batchStartDate,
        end_date: isToday ? batchStartDate : batchStartDate,
        start_time: "",
        end_time: "",
        time_taken: "",
      },
    ]);
  };

  const removeReason = (index) => {
    if (reasons.length > 1) {
      const reasonToRemove = reasons[index];
      if (reasonToRemove._id) {
        setDeletedReasonIds((prev) => [...prev, reasonToRemove._id]);
      }
      const newReasons = reasons.filter((_, i) => i !== index);
      setReasons(newReasons);
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[`reason_category_${index}`];
        delete newErrors[`reason_description_${index}`];
        delete newErrors[`reason_start_date_${index}`];
        delete newErrors[`reason_end_date_${index}`];
        delete newErrors[`reason_start_time_${index}`];
        delete newErrors[`reason_end_time_${index}`];
        return newErrors;
      });
    }
  };

  const handleBlur = (key, value) => {
    if (
      [
        "total_shift_time",
        "actual_run_hrs",
        "total_downtime",
        "shift_instance",
        "start_time",
        "end_time",
      ].includes(key) &&
      value &&
      !isValidHHMM(value)
    ) {
      setErrors((prev) => ({
        ...prev,
        [key]: "Please enter time in HH:MM format (e.g., 08:30)",
      }));
    }
    if (key === "start_date" && value && !isValidDDMMYYYY(value)) {
      setErrors((prev) => ({
        ...prev,
        [key]: "Please enter date in DD/MM/YYYY format (e.g., 02/05/2025)",
      }));
    }
  };

  const handleSubmit = async () => {
    const required = sections
      .flatMap((sec) => sec.fields)
      .filter((f) => f !== "reason")
      .map((f) => (f === "date" ? "start_date" : f))
      .concat(["machine", "line"]); // Add machine to required fields
    const newErrors = {};

    // Validate main form fields
    required.forEach((field) => {
      console.log(`Validating field ${field}:`, formData[field]);
      if (formData[field] === "" || formData[field] === undefined) {
        newErrors[field] = `${toTitleCase(field)} is required`;
      } else if (
        [
          "total_shift_time",
          "actual_run_hrs",
          "total_downtime",
          "shift_instance",
          "start_time",
          "end_time",
        ].includes(field)
      ) {
        const value = formData[field];
        const isValid =
          (typeof value === "string" && isValidHHMM(value)) ||
          (typeof value === "number" && value >= 0);
        if (!isValid) {
          newErrors[field] =
            "Please enter time in HH:MM format (e.g., 08:30) or a valid number of minutes";
        }
      } else if (field === "start_date" && !isValidDDMMYYYY(formData[field])) {
        newErrors[field] =
          "Please enter date in DD/MM/YYYY format (e.g., 02/05/2025)";
      }
    });

    // Get shift data for time range validation
    const shift = shiftData.find((s) => s.title === formData?.shift);
    const shiftStartMinutes = shift?.shift_instance
      ? isValidHHMM(shift.shift_instance)
        ? fromHHMM(shift.shift_instance)
        : toNumber(shift.shift_instance)
      : null;
    const shiftEndMinutes = shift?.total_shift_time
      ? isValidHHMM(shift.total_shift_time)
        ? fromHHMM(shift.total_shift_time)
        : toNumber(shift.total_shift_time)
      : null;

    // Validate reasons
    reasons.forEach((reason, index) => {
      console.log(`Validating reason ${index}:`, reason);
      if (!reason.category) {
        newErrors[`reason_category_${index}`] = "Reason Category is required";
      }
      if (!reason.description) {
        newErrors[`reason_description_${index}`] =
          "Reason Description is required";
      }
      if (!reason.start_date || !isValidDDMMYYYY(reason.start_date)) {
        newErrors[`reason_start_date_${index}`] =
          "Please enter start date in DD/MM/YYYY format (e.g., 02/05/2025)";
      }
      if (!reason.end_date || !isValidDDMMYYYY(reason.end_date)) {
        newErrors[`reason_end_date_${index}`] =
          "Please enter end date in DD/MM/YYYY format (e.g., 02/05/2025)";
      }
      if (!reason.start_time || !isValidHHMM(reason.start_time)) {
        newErrors[`reason_start_time_${index}`] =
          "Please enter start time in HH:MM format (e.g., 00:30)";
      }
      if (!reason.end_time || !isValidHHMM(reason.end_time)) {
        newErrors[`reason_end_time_${index}`] =
          "Please enter end time in HH:MM format (e.g., 00:30)";
      }
      if (
        reason.start_time &&
        reason.end_time &&
        isValidHHMM(reason.start_time) &&
        isValidHHMM(reason.end_time) &&
        fromHHMM(reason.end_time) <= fromHHMM(reason.start_time)
      ) {
        newErrors[`reason_end_time_${index}`] =
          "End time must be after start time";
      }
      if (
        reason.start_date &&
        reason.end_date &&
        isValidDDMMYYYY(reason.start_date) &&
        isValidDDMMYYYY(reason.end_date) &&
        new Date(toYYYYMMDD(reason.end_date)) <
          new Date(toYYYYMMDD(reason.start_date))
      ) {
        newErrors[`reason_end_date_${index}`] =
          "End date must be on or after start date";
      }
      if (
        shiftStartMinutes !== null &&
        shiftEndMinutes !== null &&
        reason.start_time &&
        isValidHHMM(reason.start_time)
      ) {
        const startTimeMinutes = fromHHMM(reason.start_time);
        if (
          startTimeMinutes < shiftStartMinutes ||
          startTimeMinutes > shiftEndMinutes
        ) {
          newErrors[`reason_start_time_${index}`] =
            `Start time must be between ${toHHMM(
              shiftStartMinutes,
            )} and ${toHHMM(shiftEndMinutes)}`;
        }
      }
      if (
        shiftStartMinutes !== null &&
        shiftEndMinutes !== null &&
        reason.end_time &&
        isValidHHMM(reason.end_time)
      ) {
        const endTimeMinutes = fromHHMM(reason.end_time);
        if (
          endTimeMinutes < shiftStartMinutes ||
          endTimeMinutes > shiftEndMinutes
        ) {
          newErrors[`reason_end_time_${index}`] =
            `End time must be between ${toHHMM(
              shiftStartMinutes,
            )} and ${toHHMM(shiftEndMinutes)}`;
        }
      }
    });

    // Validate total downtime vs. sum of reason time_taken
    const totalDowntime = formData.total_downtime
      ? isValidHHMM(formData.total_downtime)
        ? fromHHMM(formData.total_downtime)
        : toNumber(formData.total_downtime)
      : 0;
    const totalReasonTimeTaken = reasons.reduce(
      (sum, r) => sum + (r.time_taken ? Number(r.time_taken) : 0),
      0,
    );

    if (totalReasonTimeTaken !== totalDowntime) {
      newErrors.reasons = `Total time taken of reasons (${toHHMM(
        totalReasonTimeTaken,
      )}) must equal total downtime (${toHHMM(totalDowntime)})`;
    }

    // Validate formData start_time and end_time
    if (
      formData.start_time &&
      formData.end_time &&
      isValidHHMM(formData.start_time) &&
      isValidHHMM(formData.end_time) &&
      fromHHMM(formData.end_time) <= fromHHMM(formData.start_time)
    ) {
      newErrors.end_time = "End time must be after start time";
    }

    // Handle validation errors
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      toastMessage({
        message: "Please fix the errors in the form before submitting",
        type: "error",
      });
      return;
    }

    try {
      // Prepare DPR data for submission
      const submitData = {
        ...formData,
        start_date: toYYYYMMDD(formData.start_date),
        reason: reasons
          .map(
            (r) => `${r.category}: ${r.description}, ${toHHMM(r.time_taken)}`,
          )
          .join(";"),
        total_shift_time:
          formData.total_shift_time && isValidHHMM(formData.total_shift_time)
            ? fromHHMM(formData.total_shift_time)
            : formData.total_shift_time,
        actual_run_hrs:
          formData.actual_run_hrs && isValidHHMM(formData.actual_run_hrs)
            ? fromHHMM(formData.actual_run_hrs)
            : formData.actual_run_hrs,
        total_downtime:
          formData.total_downtime && isValidHHMM(formData.total_downtime)
            ? fromHHMM(formData.total_downtime)
            : formData.total_downtime,
        shift_instance:
          formData.shift_instance && isValidHHMM(formData.shift_instance)
            ? fromHHMM(formData.shift_instance)
            : formData.shift_instance,
        start_time:
          formData.start_time && isValidHHMM(formData.start_time)
            ? fromHHMM(formData.start_time)
            : formData.start_time,
        end_time:
          formData.end_time && isValidHHMM(formData.end_time)
            ? fromHHMM(formData.end_time)
            : formData.end_time,
        time_taken: formData.time_taken,
        mid: formData.machine, // Include machine in submission
      };
      console.log("Submitting DPR data:", submitData);

      // Submit DPR data
      const dprResponse = await onSubmit(submitData);
      const dprId =
        dprResponse?.data?._id ||
        dprResponse?.data?.id ||
        dprResponse?.data?.dpr_id;

      if (!dprId) {
        throw new Error(
          `Failed to retrieve DPR ID from onSubmit response: ${JSON.stringify(dprResponse)}`,
        );
      }

      console.log("DPR ID:", dprId);
      console.log("isUpdate:", !!initialData?._id);

      const isUpdate = !!initialData?._id;

      // Handle loss tree reports
      for (const reason of reasons) {
        const lossTreeData = {
          dpr_id: dprId,
          shift: formData.shift,
          line: formData.line,
          product_name: formData.product_name,
          batch_number: formData.batch_number,
          downtime_detail: reason.description,
          category: reason.category,
          start_date: toYYYYMMDD(reason.start_date),
          end_date: toYYYYMMDD(reason.end_date),
          start_time:
            reason.start_time && isValidHHMM(reason.start_time)
              ? fromHHMM(reason.start_time)
              : 0,
          end_time:
            reason.end_time && isValidHHMM(reason.end_time)
              ? fromHHMM(reason.end_time)
              : 0,
          duration: reason.time_taken || 0,
        };

        console.log(`Processing reason:`, reason);

        try {
          if (isUpdate && reason._id) {
            console.log(
              `Updating reason ${reason._id} with PUT to ${dbConfig.url}/losstreereports/${reason._id}`,
            );
            await axios.put(
              `${dbConfig.url}/losstreereports/${reason._id}`,
              lossTreeData,
            );
          } else {
            console.log(
              `Creating new reason with POST to ${dbConfig.url}/losstreereports`,
            );
            await axios.post(`${dbConfig.url}/losstreereports`, lossTreeData);
          }
        } catch (error) {
          console.error(
            `Error ${isUpdate && reason._id ? "updating" : "creating"} loss tree report:`,
            error.response?.data || error.message,
          );
          throw error;
        }
      }

      // Delete removed reasons
      for (const reasonId of deletedReasonIds) {
        try {
          console.log(
            `Deleting reason ${reasonId} with DELETE to ${dbConfig.url}/losstreereports/${reasonId}`,
          );
          await axios.delete(`${dbConfig.url}/losstreereports/${reasonId}`);
        } catch (error) {
          console.error(
            `Error deleting loss tree report ${reasonId}:`,
            error.response?.data || error.message,
          );
          throw error;
        }
      }

      setDeletedReasonIds([]);
      resetForm();
      onClose();
      toastMessageSuccess({
        message: `DPR ${isUpdate ? "updated" : "created"} successfully`,
      });
    } catch (error) {
      console.error("Submission error:", error.response?.data || error.message);
      toastMessage({
        message: `Error submitting data: ${error.message}`,
        type: "error",
      });
    }
  };

  const timeFields = [
    "total_shift_time",
    "actual_run_hrs",
    "total_downtime",
    "shift_instance",
    "start_time",
    "end_time",
    "time_taken",
  ];
  const isLastReasonComplete = () => {
    const lastReason = reasons[reasons.length - 1];
    const requiredFields = [
      "category",
      "description",
      "start_date",
      "end_date",
      "start_time",
      "end_time",
    ];

    return (
      requiredFields.every(
        (field) =>
          lastReason[field] !== undefined &&
          lastReason[field]?.toString().trim() !== "",
      ) &&
      isValidHHMM(lastReason.start_time) &&
      isValidHHMM(lastReason.end_time)
    );
  };

  // Render downtime bar
  const renderDowntimeBar = () => {
    const totalDowntime = formData.total_downtime
      ? isValidHHMM(formData.total_downtime)
        ? fromHHMM(formData.total_downtime)
        : toNumber(formData.total_downtime)
      : 0;

    if (!totalDowntime) return null;

    return (
      <Box sx={{ mt: 2, mb: 2 }}>
        <Typography variant="subtitle2">Downtime Breakdown</Typography>
        <Box
          sx={{
            display: "flex",
            height: "20px",
            width: "100%",
            border: "1px solid #ccc",
            overflow: "hidden",
          }}
        >
          {reasons.map((reason, index) => {
            const timeTaken = reason.time_taken ? Number(reason.time_taken) : 0;
            const width = totalDowntime ? (timeTaken / totalDowntime) * 100 : 0;
            return (
              <Box
                key={index}
                sx={{
                  width: `${width}%`,
                  backgroundColor: reasonColors[index % reasonColors.length],
                  position: "relative",
                }}
                title={`${reason.category}: ${toHHMM(timeTaken)}`}
              />
            );
          })}
        </Box>
        {errors.reasons && (
          <Typography variant="caption" color="error" sx={{ mt: 1 }}>
            {errors.reasons}
          </Typography>
        )}
      </Box>
    );
  };

  const renderDropdown = (fieldKey, options, value, handleChange, hasError) => (
  <FormControl fullWidth key={fieldKey} error={hasError}>
    <InputLabel id={`select-label-${fieldKey}`}>{toTitleCase(fieldKey)}</InputLabel>
    <Select
      value={value}
      label={toTitleCase(fieldKey)}
      labelId={`select-label-${fieldKey}`}
      onChange={(e) => handleChange(fieldKey, e.target.value)}
      displayEmpty
    >
      {options.map((opt) => (
        <MenuItem key={opt} value={opt}>
          {toTitleCase(opt)}
        </MenuItem>
      ))}
    </Select>
    {hasError && (
      <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
        {errors[fieldKey]}
      </Typography>
    )}
  </FormControl>
);

  const renderReadOnlyTextField = (fieldKey, value) => (
    <TextField
      key={fieldKey}
      fullWidth
      label={toTitleCase(fieldKey)}
      value={value}
      InputProps={{ readOnly: true }}
      variant="outlined"
    />
  );

  const renderField = (fieldKey) => {
    const isDropdown = dropdownKeys.includes(fieldKey) && fieldKey !== "reason";
    const value = formData[fieldKey] ?? "";
    const hasError = Boolean(errors[fieldKey]);

    if (fieldKey === "reason") {
      const categories = [...new Set(reasonsData.map((r) => r.category))];
      const allowedDates = getAllowedDates();
      const isBatchStartYesterday = formData.start_date === allowedDates[0];
      const reasonAllowedDates = [
        ...new Set([
          ...(isBatchStartYesterday
            ? allowedDates
            : [formData.start_date || allowedDates[1]]),
          ...reasons.map((r) => r.start_date).filter(Boolean),
          ...reasons.map((r) => r.end_date).filter(Boolean),
        ]),
      ];
      const shift = shiftData.find((s) => s.title === formData.shift);
      const shiftStartTime =
        shift?.shift_instance && isValidHHMM(shift.shift_instance)
          ? shift.shift_instance
          : "N/A";
      const shiftEndTime =
        shift?.total_shift_time && isValidHHMM(shift.total_shift_time)
          ? shift.total_shift_time
          : "N/A";

      return (
        <Box>
          {renderDowntimeBar()}
          <TableContainer component={Paper} sx={{ mt: 2 }}>
            <Typography variant="subtitle2" sx={{ px: 2, pt: 2 }}>
              Downtime Reasons
            </Typography>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Category</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Start Date</TableCell>
                  <TableCell>End Date</TableCell>
                  <TableCell>Start Time</TableCell>
                  <TableCell>End Time</TableCell>
                  <TableCell>Time Taken</TableCell>
                  <TableCell>Action</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {reasons.map((reason, index) => (
                  <TableRow key={`reason-${index}`}>
                    <TableCell>
                      <FormControl
                        fullWidth
                        error={Boolean(errors[`reason_category_${index}`])}
                      >
                        <Select
                          value={reason.category}
                          onChange={(e) =>
                            handleReasonChange(index, "category", e.target.value)
                          }
                        >
                          {categories.map((category) => (
                            <MenuItem key={category} value={category}>
                              {toTitleCase(category)}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </TableCell>

                    <TableCell>
                      <TextField
                        value={reason.description}
                        onChange={(e) =>
                          handleReasonChange(
                            index,
                            "description",
                            e.target.value,
                          )
                        }
                        error={Boolean(errors[`reason_description_${index}`])}
                        helperText={errors[`reason_description_${index}`]}
                        fullWidth
                      />
                    </TableCell>

                    <TableCell>
                      <FormControl
                        fullWidth
                        error={Boolean(errors[`reason_start_date_${index}`])}
                      >
                        <Select
                          value={reason.start_date}
                          onChange={(e) =>
                            handleReasonChange(
                              index,
                              "start_date",
                              e.target.value,
                            )
                          }
                        >
                          {[formData.start_date || allowedDates[1]].map(
                            (date) => (
                              <MenuItem key={date} value={date}>
                                {date}
                              </MenuItem>
                            ),
                          )}
                        </Select>
                        {errors[`reason_start_date_${index}`] && (
                          <Typography variant="caption" color="error">
                            {errors[`reason_start_date_${index}`]}
                          </Typography>
                        )}
                      </FormControl>
                    </TableCell>

                    <TableCell>
                      <FormControl
                        fullWidth
                        error={Boolean(errors[`reason_end_date_${index}`])}
                      >
                        <Select
                          value={reason.end_date}
                          onChange={(e) =>
                            handleReasonChange(
                              index,
                              "end_date",
                              e.target.value,
                            )
                          }
                        >
                          {reasonAllowedDates.map((date) => (
                            <MenuItem key={date} value={date}>
                              {date}
                            </MenuItem>
                          ))}
                        </Select>
                        {errors[`reason_end_date_${index}`] && (
                          <Typography variant="caption" color="error">
                            {errors[`reason_end_date_${index}`]}
                          </Typography>
                        )}
                      </FormControl>
                    </TableCell>

                    <TableCell>
                      <TextField
                        value={reason.start_time}
                        onChange={(e) =>
                          handleReasonChange(index, "start_time", e.target.value)
                        }
                        onBlur={(e) =>
                          !isValidHHMM(e.target.value) &&
                          setErrors((prev) => ({
                            ...prev,
                            [`reason_start_time_${index}`]:
                              "Enter time in HH:MM format",
                          }))
                        }
                        error={Boolean(errors[`reason_start_time_${index}`])}
                        fullWidth
                        helperText={
                          errors[`reason_start_time_${index}`] ||
                          (shiftStartTime !== "N/A" && shiftEndTime !== "N/A"
                            ? `Enter time between ${shiftStartTime} and ${shiftEndTime}`
                            : "")
                        }
                        placeholder="HH:MM"
                      />
                    </TableCell>

                    <TableCell>
                      <TextField
                        value={reason.end_time}
                        onChange={(e) =>
                          handleReasonChange(index, "end_time", e.target.value)
                        }
                        onBlur={(e) =>
                          !isValidHHMM(e.target.value) &&
                          setErrors((prev) => ({
                            ...prev,
                            [`reason_end_time_${index}`]:
                              "Enter time in HH:MM format",
                          }))
                        }
                        error={Boolean(errors[`reason_end_time_${index}`])}
                        fullWidth
                        helperText={
                          errors[`reason_end_time_${index}`] ||
                          (shiftStartTime !== "N/A" && shiftEndTime !== "N/A"
                            ? `Enter time between ${shiftStartTime} and ${shiftEndTime}`
                            : "")
                        }
                        placeholder="HH:MM"
                      />
                    </TableCell>

                    <TableCell>
                      <TextField
                        value={
                          reason.time_taken ? toHHMM(reason.time_taken) : ""
                        }
                        disabled
                        fullWidth
                        placeholder="HH:MM"
                      />
                    </TableCell>

                    <TableCell>
                      <IconButton
                        onClick={() => removeReason(index)}
                        disabled={reasons.length === 1}
                      >
                        <Delete />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            <Box sx={{ p: 2 }}>
              <Button
                startIcon={<Add />}
                onClick={() => {
                  if (isLastReasonComplete()) {
                    addReason();
                  } else {
                    toast.error(
                      "Please complete the current reason before adding another.",
                    );
                  }
                }}
                variant="outlined"
              >
                Add Reason
              </Button>
            </Box>
          </TableContainer>
        </Box>
      );
    }

    if (isDropdown) {
      let options = dropdownOptions[fieldKey] || [];

      if (fieldKey === "shift") {
        options = shiftData.map((s) => s.title);
      }

      if (fieldKey === "line") {
        if (Array.isArray(lineData)) {
          options = lineData.map((line) => line.title);
          return renderDropdown(fieldKey, options, value, handleChange, hasError);
        } else if (lineData?.line) {
          const match = lineTitle.find((l) => l._id === lineData.line);
          if (match) return renderReadOnlyTextField(fieldKey, match.title);
        }
        return renderReadOnlyTextField(fieldKey, "");
      }

      if (fieldKey === "product_name") {
        // Filter products based on selected machine
        let filteredProducts = productData;
        if (Array.isArray(productData) && formData.machine) {
          // Find the selected machine object by title
          let selectedMachineObj = null;
          if (Array.isArray(machineData)) {
            selectedMachineObj = machineData.find((m) => m.title === formData.machine);
          } else if (machineData?._id) {
            selectedMachineObj = machineData;
          }
          if (selectedMachineObj && selectedMachineObj._id) {
            filteredProducts = productData.filter((p) => p.mid === selectedMachineObj._id);
          }
        }
        options = filteredProducts.map((p) => p.title);
      }

      if (fieldKey === "machine") {
      // Filter machines based on selected line
      let filteredMachines = machineData;
      if (Array.isArray(machineData) && formData.line) {
        // Find the selected line object by title
        let selectedLineObj = null;
        if (Array.isArray(lineData)) {
          selectedLineObj = lineData.find((l) => l.title === formData.line);
        } else if (lineData?.line) {
          selectedLineObj = lineTitle.find((l) => l._id === lineData.line);
        }
        if (selectedLineObj && selectedLineObj._id) {
          filteredMachines = machineData.filter((m) => m.line === selectedLineObj._id);
        }
      }
      if (Array.isArray(filteredMachines)) {
        options = filteredMachines.map((m) => m.title);
        return renderDropdown(fieldKey, options, value, handleChange, hasError);
      } else if (machineData?._id) {
        const match = machineTitle.find((m) => m._id === machineData._id);
        if (match) return renderReadOnlyTextField(fieldKey, match.title);
      }
      return renderReadOnlyTextField(fieldKey, "");
    }

      return renderDropdown(fieldKey, options, value, handleChange, hasError);
    }

    if (fieldKey === "start_date") {
      const allowedDates = getAllowedDates();
      return renderDropdown(fieldKey, allowedDates, value, handleChange, hasError);
    }

    if (timeFields.includes(fieldKey)) {
      const displayValue =
        value === "" ? "" : typeof value === "number" ? toHHMM(value) : value;

      return (
        <TextField
          fullWidth
          key={fieldKey}
          type="text"
          label={`${toTitleCase(fieldKey)} (HH:MM)`}
          value={displayValue}
          onChange={(e) => handleChange(fieldKey, e.target.value)}
          onBlur={(e) => handleBlur(fieldKey, e.target.value)}
          disabled={fieldKey === "shift_instance" || fieldKey === "time_taken"}
          error={hasError}
          placeholder="HH:MM"
          helperText={errors[fieldKey]}
        />
      );
    }

    const isNum = numberFields.includes(fieldKey);
    const disabled =
      autoFilledFields.includes(fieldKey) || fieldKey === "concatenate";

    return (
      <TextField
        fullWidth
        key={fieldKey}
        type={isNum ? "number" : "text"}
        label={toTitleCase(fieldKey)}
        value={value}
        onChange={(e) =>
          handleChange(fieldKey, isNum ? Number(e.target.value) : e.target.value)
        }
        disabled={disabled}
        error={hasError}
        helperText={errors[fieldKey]}
      />
    );
  };


  return (
    <Dialog open={open} onClose={() => {
      resetForm(); // Ensure form is reset when dialog is closed
      onClose();
    }} fullWidth maxWidth="lg">
      <DialogTitle>
        {initialData
          ? toTitleCase("edit production record")
          : toTitleCase("add production record")}
      </DialogTitle>
      <DialogContent dividers>
        {sections.map(({ title, fields }) => (
          <Box key={title} mb={3}>
            <Typography variant="subtitle1" gutterBottom>
              {toTitleCase(title)}
            </Typography>
            <Grid container spacing={2}>
              {fields.map((fk) => (
                <Grid
                  item
                  xs={
                    fk === "reason"
                      ? 12
                      : title === "General Information"
                        ? 4
                        : 3
                  }
                  key={fk}
                >
                  {renderField(fk === "date" ? "start_date" : fk)}
                </Grid>
              ))}
            </Grid>
          </Box>
        ))}
      </DialogContent>
      <DialogActions>
        <ButtonBasicCancel
          type="button"
          buttonTitle="Cancel"
          onClick={() => {
            resetForm();
            onClose();
          }}
        />
        <Button
          onClick={() => {
            console.log("Submit button clicked");
            handleSubmit();
          }}
          color="primary"
          variant="contained"
        >
          {toTitleCase("submit")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DPRForm;