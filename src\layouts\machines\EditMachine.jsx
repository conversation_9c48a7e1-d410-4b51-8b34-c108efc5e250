import React, { useState, useEffect } from "react";
import { useAuth } from "../../hooks/AuthProvider";
import {
  Button,
  InputLabel,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  Grid,
  Box,
  MenuItem,
} from "@mui/material";
import { companies, companyId_constant, machines } from "../../constants/data";
import { db } from "../../firebase";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../components/buttons/Buttons";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useStateContext } from "../../context/ContextProvider";
import { themeColors } from "../../infrastructure/theme";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { useEditMachineCfr } from "../../hooks/cfr/machineCfrProvider";
import axios from "axios";
import { toastMessageSuccess, toastMessage } from "../../tools/toast";
import {
  useEditMachinesCountSetter,
  useMachinesAllGetter,
  useMachinesSetter,
} from "../../services3/machines/MachineContext2";
import {
  useEditMachines,
  useEditMachinesSetter,
} from "../../services3/machines/EditMachineContext";

const EditMachine = ({ handleClose, data, open, handleMachineData }) => {
  console.log("data11111", data);
  const editmachinecfr = useEditMachineCfr();
  const handleEditMachineCount = useEditMachinesCountSetter();
  const machines = useMachinesAllGetter();
  const [title, setTitle] = useState(data.title);
  const [count, setCount] = useState(0);
  const [desc, setDesc] = useState(data.desc);
  const [location, setLocation] = useState(data.location);
  const [equipmentId, setEquipmentId] = useState(data.equipment_id);
  const [serialNo, setSerialNo] = useState(data.serial_no);
  const [model, setModel] = useState(data.model);
  const [block, setBlock] = useState(data.block);
  const [line, setLine] = useState(data.line || ""); // Line state for _id
  const [lineData, setLineData] = useState([]); // Array of all lines
  const [warranty, setWarranty] = useState(data.warranty);
  const [status, setStatus] = useState(data.status);
  const [createdBy, setCreatedBy] = useState(data.created_by);
  const { currentColor, currentMode, currentColorLight } = useStateContext();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const { currentUser } = useAuth();
  const allMachinesSetter = useMachinesSetter();

  const getLineData = async () => {
    try {
      const response = await axios.get(`${dbConfig.url}/lines`); // Fetch all lines
      const data = response.data.data || [];
      setLineData(data); // Store array of lines
    } catch (error) {
      console.error("Error fetching line data:", error);
      toastMessage({ message: "Failed to fetch lines" });
    }
  };

  useEffect(() => {
    getLineData();
  }, []);

  // Reset state when `data` changes
  useEffect(() => {
    setTitle(data.title);
    setDesc(data.desc);
    setLocation(data.location);
    setEquipmentId(data.equipment_id);
    setSerialNo(data.serial_no);
    setModel(data.model);
    setBlock(data.block);
    setLine(data.line || ""); // Reset line state to _id
    setWarranty(data.warranty);
    setStatus(data.status);
    setCreatedBy(data.created_by); // Note: Should this be created_by or created_at?
  }, [data]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    const date = new Date();
    const dataForCfr = {
      activity: "machine edited",
      dateTime: date,
      description: "a machine is edited",
      machine: data._id, // Use the actual machine ID
      module: "Machine",
      username: currentUser.username,
    };

    // Use snake_case keys to match backend schema
    const dataSet = {
      title: title.trim(),
      desc: desc.trim(),
      location: location.trim(),
      equipment_id: equipmentId.trim(),
      serial_no: serialNo.trim(),
      model: model.trim(),
      block: block.trim(),
      line: line.trim(), // Send line _id
      warranty: warranty.trim(),
      status: status.trim(),
      created_by: createdBy,
    };

    try {
      await axios.put(`${dbConfig.url}/machines/${data?._id}`, dataSet);
      editmachinecfr(dataForCfr);
      handleEditMachineCount();
      toastMessageSuccess({
        message: `Updated Machine successfully!`,
      });

      // Update context
      const updatedMachine = { ...data, ...dataSet, _id: data._id };
      allMachinesSetter((prevMachines) =>
        prevMachines.map((m) => (m._id === data._id ? updatedMachine : m)),
      );

      handleClose();
    } catch (err) {
      toastMessage({ message: err.message });
    }
  };

  // Reset state when dialog is closed
  const handleCancel = () => {
    setTitle(data.title);
    setDesc(data.desc);
    setLocation(data.location);
    setEquipmentId(data.equipment_id);
    setSerialNo(data.serial_no);
    setModel(data.model);
    setBlock(data.block);
    setLine(data.line || "");
    setWarranty(data.warranty);
    setStatus(data.status);
    setCreatedBy(data.created_by); // Note: Should this be created_by or created_at?
    handleClose();
  };

  return (
    <Dialog open={open} fullWidth onClose={handleCancel}>
      <DialogTitle
        style={
          currentMode === "Dark"
            ? { backgroundColor: themeColors.dark.secondary }
            : { backgroundColor: "white" }
        }
      >
        Edit Machine - [{data.title}]
      </DialogTitle>
      <DialogContent
        style={
          currentMode === "Dark"
            ? { backgroundColor: themeColors.dark.secondary }
            : { backgroundColor: "white" }
        }
      >
        <form onSubmit={handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <InputLabel style={{ marginBottom: "8px" }}>
                Machine Name
              </InputLabel>
              <TextField
                onChange={(e) => setTitle(e.target.value)}
                value={title}
                onBlur={() => setTitle(title?.trim())}
                required
                variant="outlined"
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InputLabel style={{ marginBottom: "8px" }}>
                Equipment ID
              </InputLabel>
              <TextField
                onChange={(e) => setEquipmentId(e.target.value)}
                value={equipmentId}
                onBlur={() => setEquipmentId(equipmentId?.trim())}
                required
                variant="outlined"
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InputLabel style={{ marginBottom: "8px" }}>Serial No</InputLabel>
              <TextField
                onChange={(e) => setSerialNo(e.target.value)}
                value={serialNo}
                onBlur={() => setSerialNo(serialNo?.trim())}
                required
                variant="outlined"
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InputLabel style={{ marginBottom: "8px" }}>Model</InputLabel>
              <TextField
                onChange={(e) => setModel(e.target.value)}
                value={model}
                onBlur={() => setModel(model?.trim())}
                required
                variant="outlined"
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InputLabel style={{ marginBottom: "8px" }}>Block</InputLabel>
              <TextField
                onChange={(e) => setBlock(e.target.value)}
                value={block}
                onBlur={() => setBlock(block?.trim())}
                required
                variant="outlined"
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InputLabel style={{ marginBottom: "8px" }}>
                Machine Location
              </InputLabel>
              <TextField
                onChange={(e) => setLocation(e.target.value)}
                value={location}
                onBlur={() => setLocation(location?.trim())}
                required
                variant="outlined"
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InputLabel style={{ marginBottom: "8px" }}>Warranty</InputLabel>
              <TextField
                onChange={(e) => setWarranty(e.target.value)}
                value={warranty}
                onBlur={() => setWarranty(warranty?.trim())}
                required
                variant="outlined"
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InputLabel style={{ marginBottom: "8px" }}>Status</InputLabel>
              <TextField
                onChange={(e) => setStatus(e.target.value)}
                value={status}
                onBlur={() => setStatus(status?.trim())}
                required
                variant="outlined"
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InputLabel style={{ marginBottom: "8px" }}>Line</InputLabel>
              <TextField
                select
                value={line}
                onChange={(e) => setLine(e.target.value)}
                variant="outlined"
                fullWidth
                required
              >
                <MenuItem value="" disabled>
                  Select a line
                </MenuItem>
                {lineData.map((lineItem) => (
                  <MenuItem key={lineItem._id} value={lineItem._id}>
                    {lineItem.title}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <InputLabel style={{ marginBottom: "8px" }}>
                Machine Description
              </InputLabel>
              <TextField
                onChange={(e) => setDesc(e.target.value)}
                value={desc}
                onBlur={() => setDesc(desc?.trim())}
                required
                variant="outlined"
                fullWidth
                multiline
                rows={4}
              />
            </Grid>
            <Grid item xs={12}>
              <Box display="flex" justifyContent="space-between" mt={2}>
                <ButtonBasicCancel
                  buttonTitle="Cancel"
                  type="button"
                  onClick={handleCancel}
                />
                <ButtonBasic buttonTitle="Submit" type="submit" />
              </Box>
            </Grid>
          </Grid>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditMachine;