import React, {useEffect, useState, useMemo, useCallback} from "react";
import {useParams, useNavigate, useLocation} from "react-router-dom";
// import "../machineData.scss";
import MachineDataHeader from "../MachineDataHeader";
import axios from "axios";
import {dbConfig} from "../../../../src/infrastructure/db/db-config";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import SearchIcon from "@mui/icons-material/Search";
import TablePagination from "@mui/material/TablePagination";
import {
  Dialog,
  DialogContent,
  <PERSON><PERSON>,
  Badge,
  DialogTitle,
  CircularProgress,
  TextField,
  Typography,
  <PERSON>lt<PERSON>,
  InputAdornment,
  IconButton,
} from "@mui/material";
import {useStateContext} from "../../../context/ContextProvider";
import {sharedCss} from "../../../styles/sharedCss";
import AttachMaintenance from "./AttachMaintenance";
import CommonDropDown from "../../../components/commons/dropDown.component";
import TableHeader from "../TableHeader";
import NoDataComponent from "../../../components/commons/noData.component";
import {Edit, Visibility} from "@mui/icons-material";
import DeleteIcon from "@mui/icons-material/Delete";
import {useCommonOuterContainerStyle} from "../../../styles/useCommonOuterContainerStyle";
import {useCheckAccess} from "../../../utils/useCheckAccess";
import NotAccessible from "../../../components/not-accessible/not-accessible";
import AddAlarm from "./AddAlarm";
import EditAlarm from "./EditAlarm";
import Delete from "../../../components/Delete/Delete";
import HandleAlarmSop from "./HandleAlarmSop";
import MaintenanceItem from "../../machineData/MaintenanceItem";

const AlarmsNew = () => {
  const [open, setOpen] = useState(false);
  const [open2, setOpen2] = useState(false);
  const {mid} = useParams();
  const history = useNavigate();
  const location = useLocation();
  const {alarmSopIdInitial = "", defaultAlarmType = "Alarms"} =
    location?.state || {};

  const [alarmSopId, setAlarmSopId] = useState(alarmSopIdInitial || "");

  const expandedByDefault = useCallback(
    data => {
      return alarmSopId === data._id;
    },
    [alarmSopId, alarmSopIdInitial],
  );

  const [searchTerm, setSearchTerm] = useState("");
  const [defaultType, setDefaultType] = useState(1);
  const [type, setType] = useState(defaultAlarmType);
  const [machineName, setMachineName] = useState("");
  const [dataLoading, setDataLoading] = useState(true);
  const {currentColor, currentMode, currentColorLight} = useStateContext();

  const [liveAlarmsAll, setLiveAlarmsAll] = useState([]);
  const [activeAlarmsAll, setActiveAlarmsAll] = useState([]);
  const [maintenanceOfMachine, setMaintenanceOfMachine] = useState([]);
  const [newLiveAlarms, setNewLiveAlarms] = useState([]);
  const [alarmIdOfClicked, setAlarmIdOfClicked] = useState("");
  const [addAlarmOpen, setAddAlarmOpen] = useState(false);
  const [editAlarmOpen, setEditAlarmOpen] = useState(false);
  const [selectedAlarm, setSelectedAlarm] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [openDel, setOpenDel] = useState(false);
  const [selectedAlarmIdToDelete, setSelectedAlarmIdToDelete] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10); // Default to 10 items per page

  const [addAlarmSopOpen, setAddAlarmSopOpen] = useState(false);
  const [alarmSopDataByMachineId, setAlarmSopDataByMachineId] = useState([]);

  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  // Whether dedicated steps or supsteps are needed
  const types = [
    {label: "Alarms", value: "Alarms"},
    {label: "Alarm SOP", value: "Alarm SOP"},
  ];

  const endpointForAccessCheck = type === "Alarms" ? "alarms_new" : "alarmSop";

  const hasGETAccess = useCheckAccess(endpointForAccessCheck, "GET");
  const hasPUTAccess = useCheckAccess(endpointForAccessCheck, "PUT");

  const fetchMaintenaceData = () => {
    axios
      .get(`${dbConfig.url}/maintenance/getFromMachine/${mid}`)
      .then(response => {
        const data = response.data?.data;
        const sortedData = data?.filter(data => data?.mid == mid);
        setMaintenanceOfMachine(sortedData);
      })
      .catch(error => {
        console.log("maintenance data fetch error:", error);
      });
  };

  const fetchAlarmsData = () => {
    setDataLoading(true);

    axios
      .get(`${dbConfig.url}/alarms_new`)
      .then(response => {
        if (response.data?.success && Array.isArray(response.data.data)) {
          const filteredData = response.data.data.filter(
            item => item.mid === mid,
          );
          setNewLiveAlarms(filteredData);
        } else {
          console.warn("Unexpected data format:", response.data);
          setNewLiveAlarms([]);
        }
      })
      .catch(err => {
        console.error("Error fetching alarms data:", err);
        setNewLiveAlarms([]);
      })
      .finally(() => {
        setDataLoading(false);
      });
  };

  const fetchAlarmSopData = async () => {
    setDataLoading(true);
    try {
      const alarmSopResponse = await axios.get(
        `${dbConfig.url}/alarmSop/getFromMachine/${mid}`,
      );

      const alarmSopData = alarmSopResponse.data?.data;
      setAlarmSopDataByMachineId(alarmSopData);
    } catch (error) {
      console.error("Error fetching alarm SOP data:\n", error);
      setAlarmSopDataByMachineId([]);
    } finally {
      setDataLoading(false);
    }
  };

  useEffect(() => {
    fetchAlarmsData();
    fetchMaintenaceData();
    fetchAlarmSopData();
  }, []);

  useEffect(() => {
    if (type === "Alarms") {
      setAlarmSopId("");
    }
  }, [type]);

  const handleOpenAddAlarmOrAlarmSop = () => {
    if (type !== "Alarms") {
      return setAddAlarmSopOpen(true);
    }

    setAddAlarmOpen(true);
  };

  const handleCloseAddAlarmOrAlarmSop = () => {
    if (type !== "Alarms") {
      return setAddAlarmSopOpen(false);
    }
    setAddAlarmOpen(false);
  };

  const handleOnChangeSearchTerm = e => {
    setSearchTerm(e.target.value);
  };

  const defaultTypeSetter = type => {
    setDefaultType(type);
  };

  const typeSetter = e => {
    setType(e.target.value);
  };

  const handleOpenForAttachMaintenance = alarmInfoOfClicked => {
    setAlarmIdOfClicked(alarmInfoOfClicked?._id);
    setOpen2(true);
  };

  const handleOpenDeleteDialog = alarmId => {
    setSelectedAlarmIdToDelete(alarmId);
    setOpenDel(true);
  };

  const onConfirmDeleteAlarm = async () => {
    if (!selectedAlarmIdToDelete) return;
    setDeleteLoading(true);
    try {
      await axios.delete(
        `${dbConfig.url}/alarms_new/${selectedAlarmIdToDelete}`,
      );
      fetchAlarmsData();
    } catch (err) {
      alert("Failed to delete alarm.");
      console.error("Delete alarm error:", err);
    } finally {
      setDeleteLoading(false);
      setOpenDel(false);
      setSelectedAlarmIdToDelete(null);
    }
  };

  const commonCss = sharedCss();

  const filterNewAlarms = sData => {
    if (!searchTerm) return true;
    const lowerSearch = searchTerm.toLowerCase();
    return (
      Object.values(sData).some(
        v => typeof v === "string" && v.toLowerCase().includes(lowerSearch),
      ) && type === "Alarms"
    );
  };

  const filterNewAlarmSop = sData => {
    if (!searchTerm) return true;
    const lowerSearch = searchTerm.toLowerCase();
    return (
      Object.values(sData).some(
        v => typeof v === "string" && v.toLowerCase().includes(lowerSearch),
      ) && type === "Alarm SOP"
    );
  };

  const filteredAlarms = useMemo(
    () =>
      (type === "Alarms" && newLiveAlarms.filter(filterNewAlarms)) ||
      (type === "Alarm SOP" &&
        alarmSopDataByMachineId.filter(filterNewAlarmSop)),
    [newLiveAlarms, searchTerm, alarmSopDataByMachineId, type],
  );

  const paginatedAlarms = useMemo(() => {
    const startIndex = page * rowsPerPage;
    return filteredAlarms.slice(startIndex, startIndex + rowsPerPage);
  }, [filteredAlarms, page, rowsPerPage]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = event => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0); // reset to first page on change
  };

  const isFilteredAlarmDataAvailable = filteredAlarms.length > 0;
  const handleAlarmSopRedirect = useCallback(
    pData => {
      setAlarmSopId(pData?.alarm_sop_id);
      typeSetter({target: {value: "Alarm SOP"}});
    },
    [typeSetter, setAlarmSopId],
  );

  const handleShowRedirectMaintenanceSopIcon = useCallback(data => {
    if (!data) {
      return false;
    }

    const redirectMaintenanceSop =
      typeof data?.sop_type !== "string" || data?.sop_type === "Maintenance";
    if (redirectMaintenanceSop) {
      return !!data?.main_id;
    }
  }, []);

  const handleShowRedirectAlarmSopIcon = useCallback(data => {
    if (!data) {
      return false;
    }

    return !!data?.alarm_sop_id && data?.sop_type === "Alarm";
  }, []);

  return (
    <section>
      <MachineDataHeader />
      <div>
        {defaultType === 1 && (
          <div
            className={`${commonCss.sectionContainer} ${commonCss.backgroundLight} border-radius-outer`}>
            {hasGETAccess ? (
              <>
                <div className={commonCss.tableLable}>
                  <Typography fontWeight="bold" variant="h6">
                    Alarms
                  </Typography>
                  <div
                    className={commonCss.tableRightContent}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "1 rem",
                    }}>
                    <div>
                      <Tooltip
                        title="Search by Tag, Description, Code, Reason, Action Taken, Type, or Value"
                        arrow
                        placement="top">
                        <TextField
                          label="Search"
                          className={`${commonCss.searchBox} ${commonCss.inputAlignmentFix}`}
                          id="outlined-size-small"
                          value={searchTerm}
                          size="small"
                          onChange={e => handleOnChangeSearchTerm(e)}
                          type="text"
                          placeholder="Search Tag, Description, Code, Reason, Action Taken, Type, Value..."
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="end">
                                <SearchIcon style={{opacity: "0.5"}} />
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Tooltip>
                    </div>
                    <div>
                      <CommonDropDown
                        dropDownLabel={"Select Type"}
                        dropDownSize={"small"}
                        menuData={types}
                        menuValue={type}
                        menuValueDefault={"Maintenance"}
                        menuItemDisplay={"label"}
                        menuItemValue={"value"}
                        handleChange={typeSetter}
                        dropDownContainerStyle={{width: "200px"}}
                      />
                    </div>
                    <div>
                      <Button
                        variant="contained"
                        onClick={handleOpenAddAlarmOrAlarmSop}>
                        {type === "Alarms" ? "Add Alarm" : "Add Alarm SOP"}
                      </Button>
                    </div>
                  </div>
                </div>
                <div>
                  <TableContainer
                    component={Paper}
                    className="table border-radius-inner"
                    sx={commonOuterContainerStyle}>
                    <Table sx={{minWidth: 650}}>
                      <TableHeader
                        currentMode={currentMode}
                        columns={
                          type !== "Alarms"
                            ? [
                                {
                                  label: "Title",
                                  align: "left",
                                  width: "30%",
                                },
                                {
                                  label: "Description",
                                  align: "left",
                                  width: "30%",
                                },
                                // Dummy title row item to fill the table header
                                {label: ""},
                                {
                                  label: "Last Done",
                                  align: "left",
                                  width: "10%",
                                },
                                {
                                  label: "Status",
                                  align: "center",
                                  width: "10%",
                                },
                                {
                                  label: "Actions",
                                  align: "center",
                                  width: "10%",
                                },
                              ]
                            : [
                                {label: "Tag", align: "left"},
                                {label: "Description", align: "left"},
                                {label: "Code", align: "left"},
                                {label: "Reason", align: "left"},
                                {label: "Action Taken", align: "left"},
                                {label: "Type", align: "left"},
                                {label: "Current Value", align: "left"},
                                {label: "Status", align: "center"},
                                {label: "Actions", align: "center"},
                              ]
                        }
                      />
                      <TableBody>
                        {paginatedAlarms.length > 0 &&
                          type === "Alarms" &&
                          paginatedAlarms.map((pData, index) => (
                            <TableRow
                              key={pData?._id ?? index}
                              hover
                              sx={{
                                "&:last-child td, &:last-child th": {
                                  border: 0,
                                },
                                "&:hover": {bgcolor: "#f5f5f5"},
                                borderBottom:
                                  paginatedAlarms.length - 1 === index
                                    ? "none"
                                    : "0.05rem solid #e0e0e0",
                              }}
                              className="hover:shadow-md  hover:cursor-pointer">
                              {/* Tag */}
                              <TableCell align="left">
                                {pData?.tag ?? (
                                  <Tooltip title="Not available from backend">
                                    NA
                                  </Tooltip>
                                )}
                              </TableCell>
                              {/* Alarm Description */}
                              <TableCell align="left">
                                {pData?.desc ? (
                                  pData.desc
                                ) : (
                                  <Tooltip title="Not available from backend">
                                    NA
                                  </Tooltip>
                                )}
                              </TableCell>
                              {/* Alarm Code */}
                              <TableCell align="left">
                                {pData?.code ? (
                                  pData.code
                                ) : (
                                  <Tooltip title="Not available from backend">
                                    NA
                                  </Tooltip>
                                )}
                              </TableCell>
                              {/* Reason For Alarm */}
                              <TableCell align="left">
                                {pData?.reason ? (
                                  pData.reason
                                ) : (
                                  <Tooltip title="Not available from backend">
                                    NA
                                  </Tooltip>
                                )}
                              </TableCell>
                              {/* Action Taken */}
                              <TableCell align="left">
                                {pData?.action_taken ? (
                                  pData.action_taken
                                ) : (
                                  <Tooltip title="Not available from backend">
                                    NA
                                  </Tooltip>
                                )}
                              </TableCell>
                              {/* Type */}
                              <TableCell align="left">
                                {(() => {
                                  switch (pData?.type) {
                                    case 0:
                                      return "Process";
                                    case 1:
                                      return "Service";
                                    case 2:
                                      return "System";
                                    default:
                                      return typeof pData?.type !==
                                        "undefined" ? (
                                        pData.type
                                      ) : (
                                        <Tooltip title="Not available from backend">
                                          NA
                                        </Tooltip>
                                      );
                                  }
                                })()}
                              </TableCell>
                              {/* Current Value with Default Value as Tooltip */}
                              <TableCell align="left">
                                <Tooltip
                                  title={`Default Value: ${
                                    pData?.def_value ?? "NA"
                                  }`}>
                                  <span>
                                    {pData?.value ?? (
                                      <Tooltip title="Not available from backend">
                                        NA
                                      </Tooltip>
                                    )}
                                  </span>
                                </Tooltip>
                              </TableCell>
                              {/* Alarms Status */}
                              <TableCell align="center">
                                {pData?.def_value == pData?.value ? (
                                  <Badge color="success" variant="dot" />
                                ) : (
                                  <Badge color="error" variant="dot" />
                                )}
                              </TableCell>
                              {/* Actions */}
                              <TableCell align="left">
                                <div className="flex gap-2 justify-center">
                                  {/* Edit Alarm Button */}
                                  <Tooltip title="Edit Alarm" arrow>
                                    <IconButton
                                      onClick={() => {
                                        setSelectedAlarm(pData);
                                        setEditAlarmOpen(true);
                                      }}
                                      variant="outlined"
                                      size="small"
                                      color="primary"
                                      sx={{minWidth: "auto"}}
                                      disabled={!hasPUTAccess}>
                                      <Edit sx={{fontSize: 20}} />
                                    </IconButton>
                                  </Tooltip>
                                  {/* Delete Alarm Button */}
                                  <Tooltip title="Delete Alarm" arrow>
                                    <span>
                                      <IconButton
                                        onClick={() =>
                                          handleOpenDeleteDialog(pData?._id)
                                        }
                                        variant="outlined"
                                        size="small"
                                        color="error"
                                        sx={{minWidth: "auto"}}
                                        disabled={
                                          deleteLoading || !hasPUTAccess
                                        }>
                                        <DeleteIcon sx={{fontSize: 20}} />
                                      </IconButton>
                                    </span>
                                  </Tooltip>
                                  {handleShowRedirectMaintenanceSopIcon(
                                    pData,
                                  ) && (
                                    <Tooltip title="View Details" arrow>
                                      <IconButton
                                        onClick={() => {
                                          const commonState = {
                                            alarmId: pData?._id,
                                            mainId: pData?.main_id,
                                            type: pData?.type,
                                          };
                                          switch (pData?.type) {
                                            case 0:
                                              history(`/calibration/${mid}`, {
                                                state: commonState,
                                              });
                                              break;
                                            case 1:
                                              history(`/maintenance/${mid}`, {
                                                state: commonState,
                                              });
                                              break;
                                            case 3:
                                              history(`/maintenance/${mid}`, {
                                                state: commonState,
                                              });
                                              break;
                                            case 4:
                                              history(`/gemba/${mid}`, {
                                                state: commonState,
                                              });
                                              break;
                                            case 5:
                                              history(`/lineClearance/${mid}`, {
                                                state: commonState,
                                              });
                                              break;
                                            default:
                                              console.warn(
                                                `Unhandled type: ${pData?.type}`,
                                              );
                                              break;
                                          }
                                        }}
                                        variant="outlined"
                                        size="small"
                                        color="success"
                                        sx={{minWidth: "auto"}}>
                                        <Visibility size={20} />
                                      </IconButton>
                                    </Tooltip>
                                  )}
                                  {handleShowRedirectAlarmSopIcon(pData) && (
                                    <Tooltip
                                      title={"View alarm SOP details"}
                                      arrow>
                                      <IconButton
                                        onClick={() =>
                                          handleAlarmSopRedirect(pData)
                                        }
                                        variant="outlined"
                                        size="small"
                                        color="info"
                                        sx={{minWidth: "auto"}}>
                                        <Visibility size={20} />
                                      </IconButton>
                                    </Tooltip>
                                  )}
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        {paginatedAlarms.length > 0 &&
                          type !== "Alarms" &&
                          paginatedAlarms?.map((pData, index) => (
                            // Temporarily using Maintenance Item
                            <MaintenanceItem
                              key={pData?._id}
                              data={pData}
                              machineName={mid}
                              lastItem={index === paginatedAlarms?.length - 1}
                              useAt={type}
                              onAlarmSopEditOrDeleteSuccess={fetchAlarmSopData}
                              expandedByDefault={expandedByDefault(pData)}
                            />
                          ))}
                        {(!filteredAlarms.length ||
                          !isFilteredAlarmDataAvailable) && (
                          <>
                            <NoDataComponent
                              dataLoading={dataLoading}
                              cellColSpan={9}
                            />
                          </>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  {filteredAlarms.length > 0 && (
                    <TablePagination
                      component="div"
                      count={filteredAlarms.length}
                      rowsPerPage={rowsPerPage}
                      page={page}
                      onPageChange={handleChangePage}
                      onRowsPerPageChange={handleChangeRowsPerPage}
                      className={commonCss.tablePagination}
                    />
                  )}
                </div>
              </>
            ) : (
              <NotAccessible />
            )}
          </div>
        )}
      </div>
      {/* AddAlarm Dialog */}
      <AddAlarm
        open={addAlarmOpen}
        handleClose={handleCloseAddAlarmOrAlarmSop}
        machineId={mid}
        onAlarmAdded={fetchAlarmsData}
        maintenanceOfMachine={maintenanceOfMachine}
        alarmSopData={alarmSopDataByMachineId}
      />
      {/* EditAlarm Dialog */}
      <EditAlarm
        open={editAlarmOpen}
        handleClose={() => setEditAlarmOpen(false)}
        alarm={selectedAlarm}
        onAlarmUpdated={fetchAlarmsData}
        maintenanceOfMachine={maintenanceOfMachine}
        alarmSopData={alarmSopDataByMachineId}
      />
      {/* Delete Dialog */}
      <Dialog open={openDel}>
        <Delete
          onClose={() => setOpenDel(false)}
          onDelete={onConfirmDeleteAlarm}
        />
      </Dialog>

      {/*Handle alarm SOP data */}
      <HandleAlarmSop
        useAt={"Add"}
        showDialog={addAlarmSopOpen}
        machineId={mid}
        handleClose={handleCloseAddAlarmOrAlarmSop}
        handleSubmitSuccess={fetchAlarmSopData}
      />
    </section>
  );
};

export default AlarmsNew;
