import React, {useContext, useEffect, useRef, useState} from "react";
import {useParams} from "react-router-dom";

import {DropzoneArea} from "material-ui-dropzone";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Box,
  Autocomplete,
} from "@mui/material";
import {ButtonBasic, SubmitButtons} from "../../components/buttons/Buttons";
import {dbConfig} from "../../infrastructure/db/db-config";
import {convertBase64} from "../../hooks/useBase64";
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import {useMaintenanceInfo} from "../../context/MaintenanceContext";
import {themeColors} from "../../infrastructure/theme";
import axios from "axios";
import {useMongoRefresh} from "../../services/mongo-refresh.context";
import {HashtagContext} from "../../services2/hashtag/hashtag.context";
import {useAuth} from "../../hooks/AuthProvider";
import {useEditMachineCfr} from "../../hooks/cfr/machineCfrProvider";
import GetPreviewComponent from "../../components/commons/getPreview.component";
import {useStateContext} from "../../context/ContextProvider";

const EditStep = ({
  collectionName,
  mType,
  step,
  handleClose,
  machineName,
  name,
  userName,
  stepTitle,
  moduleName,
  parent,
  sensorList,
  open,
  setOpenEdit,
  hashtagArray2,
}) => {
  const {currentUser} = useAuth();
  const {mid} = useParams();

  const edittrainigstepcfr = useEditMachineCfr();
  const editmaintenancestepcfr = useEditMachineCfr();

  const [title, setTitle] = useState("");
  const [desc, setDesc] = useState("");
  const [ans, setAns] = useState("");
  const [type, setType] = useState("");
  const [format, setFormat] = useState("");
  const [file, setFile] = useState(null);
  const [sensor, setSensor] = useState([]); // Stores array of _id
  const [sensorValue, setSensorValue] = useState(""); // Stores _id
  const [qrcodeValue, setQrcodeValue] = useState("");
  const [qrcodes, setQrcodes] = useState([]);
  const typesImages = ["image/png", "image/jpeg", "image/jpg"];
  const videoTypes = ["video/mp4", "video/mkv", "video/mov"];
  const audioTypes = ["audio/mp3", "audio/mpeg"];
  const {currentColor, currentMode, currentColorLight} = useStateContext();
  const [hashtag, setHashtag] = useState([]);
  const [imageUrl, setImageUrl] = useState("");
  const [selectedFileForStorage, setSelectedFileForStorage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [errorMessage2, setErrorMessage2] = useState("");
  const [uploadProgress, setUploadProgress] = useState(0);
  const [loading, setLoading] = useState(false);
  const menuItemTheme = {
    backgroundColor:
      currentMode === "Dark"
        ? themeColors.dark.primary
        : themeColors.light.primary,
  };

  const {
    hashes,
    handleHastageData,
    tempHash,
    setTempHash,
    tempHashes,
    setTempHashes,
    hashArray,
    setHashArray,
    machines,
    maint,
    stepArray,
    mapForVis,
    temptemp,
    setTempTemp,
  } = useContext(HashtagContext);
  const searchInput = useRef(null);
  const [sortBy, setSortBy] = useState("none");
  const [findBy, setFindBy] = useState("");
  const [inputFindBy, setInputFindBy] = useState("");
  const [hashdata, sethashdata] = useState("");

  const [hashtitle, setHashtitle] = useState("");
  const [hashtagArray, setHashtagArray] = useState([]);
  const {refreshCount, setRefreshCount} = useMongoRefresh();
  const [hashOBJ, sethashOBJ] = useState([]);

  // Reset state when dialog opens or step changes
  useEffect(() => {
    if (open) {
      // Reset state
      setTitle(step.title || "");
      setDesc(step.desc || "");
      setAns(step.ans || "");
      setType(step.type || "");
      setFormat(step.format || "");
      setFile(null);
      setSensor(step.sensor || []);
      setSensorValue("");
      setQrcodeValue("");
      setQrcodes(step.qr_codes || []);
      setHashtag("hashtag" in step ? step.hashtag : []);
      setImageUrl("");
      setSelectedFileForStorage("");
      setErrorMessage("");
      setErrorMessage2("");
      setHashtagArray([]);
      setHashtitle("");
      setFindBy("");
      setInputFindBy("");
      sethashdata("");
      sethashOBJ([]);

      // Fetch hashtag titles for step.hashtag
      if (Array.isArray(step.hashtag) && step.hashtag.length > 0) {
        Promise.all(
          step.hashtag.map(item =>
            axios
              .get(`${dbConfig.url}/hashtags/${item}`)
              .then(response => ({
                title: response?.data?.data?.title,
                id: response?.data?.data?._id,
              }))
              .catch(error => {
                console.log(error);
                return null;
              }),
          ),
        ).then(results => {
          const validResults = results.filter(result => result !== null);
          setHashtagArray(validResults.map(result => result.title));
          sethashOBJ(validResults);
        });
      }
    }
  }, [open, step]);

  const deletingHashTag = () => {
    hashOBJ.map(item => {
      if (hashtagArray2?.includes(item.title)) {
        axios.delete(`${dbConfig.url}/hashtags/${item.id}`).catch(error => {
          toastMessage({message: error.message});
        });
      }
    });
  };

  // const hashtagfunction = () => {
  //   if (Array.isArray(hashtag))
  //     hashtag?.map((item) => {
  //       axios
  //         .get(`${dbConfig.url}/hashtags/${item}`)
  //         .then((response) => {
  //           if (hashtagArray?.length === 0) {
  //             setHashtagArray((prevState) => [
  //               ...prevState,
  //               response?.data?.data?.title,
  //             ]);
  //             sethashOBJ((prevState) => [
  //               ...prevState,
  //               {
  //                 title: response?.data?.data?.title,
  //                 id: response?.data?.data?._id,
  //               },
  //             ]);
  //           }
  //         })
  //         .catch((error) => {
  //           console.log(error);
  //         });
  //     });
  // };

  // useEffect(() => {
  //   hashtagfunction();
  // }, [refreshCount]);

  const handleButtonClickold = () => {
    if (!findBy?.trim()) {
      return toastMessageWarning({
        message: "Select a hashtag to continue",
        type: "warning",
      });
    }
    if (hashdata?.trim() !== "") {
      setHashtagArray(prevState => [...prevState, hashdata]);
      setHashtitle("");
    }
  };

  const handleButtonClick = () => {
    if (!hashtitle?.trim()) {
      return toastMessageWarning({
        message: "Enter a hashtag",
        type: "warning",
      });
    }
    if (hashtitle.trim() !== "") {
      setHashtagArray(prevState => [...prevState, hashtitle]);
      setHashtitle("");
    }
  };

  const removeHashtag = index => {
    setHashtagArray(hashtagArray?.filter((_, i) => i !== index));
  };

  const handleChanges = target => {
    setSortBy("");
    setInputFindBy(target);
    setFindBy(target);
    const arr = temptemp?.filter(options =>
      options.id.toLowerCase().includes(target.toLowerCase()),
    );
    if (target.trim() !== "") setTempHashes([...arr]);
    else setTempHashes(temptemp);
    sethashdata(arr[0]?.id || "");
  };

  const handleChange = async loadedFiles => {
    let selectedFile = loadedFiles[0];
    const base64 = await convertBase64(selectedFile);
    setSelectedFileForStorage(selectedFile);

    if (selectedFile) {
      if (type === "" || format === "") {
        toastMessage({
          message: "Please Select a Type / Format first to proceed",
        });
        return;
      }
      if (format === "image") {
        if (typesImages.includes(selectedFile.type)) {
          setFile(selectedFile);
          setImageUrl(base64);
        } else {
          setFile(null);
          toastMessage({message: "Please select an image file (png or jpg)"});
        }
      } else if (format === "video") {
        if (videoTypes.includes(selectedFile.type)) {
          setFile(selectedFile);
          setImageUrl(base64);
        } else {
          setFile(null);
          toastMessage({message: "Please select a video file (mp4 or mkv)"});
        }
      } else if (format === "audio") {
        if (audioTypes.includes(selectedFile.type)) {
          setFile(selectedFile);
          setImageUrl(base64);
        } else {
          setFile(null);
          toastMessage({message: "Please select an audio file (mp3)"});
        }
      }
    }
  };

  const handleDelete = async () => {
    setImageUrl("");
    setFile(null);
    setSelectedFileForStorage("");
  };

  const handleTitleChange = e => {
    const newValue = e.target.value;
    setErrorMessage(newValue.trim() === "" ? "Title cannot be Empty" : "");
    setTitle(e.target.value);
  };

  const handleDescChange = e => {
    const newValue = e.target.value;
    setErrorMessage2(
      newValue.trim() === "" ? "Description cannot be Empty" : "",
    );
    setDesc(e.target.value);
  };

  const handleAnsChange = e => {
    setAns(e.target.value);
  };
  const prePopData = sensorId => {
    setSensor(pre => pre.filter(dataItem => dataItem !== sensorId));
  };

  const prePopDataQr = idx => {
    let temp = qrcodes;
    temp.splice(idx, 1);
    setQrcodes([...temp]);
  };

  const isMediaValid = () => {
    if (["image", "video", "audio"].includes(format)) {
      if (selectedFileForStorage) return true;
      if (!step.url) return false;
      return step.format === format;
    }
    return true;
  };

  const handleSubmit = async e => {
    e.preventDefault();

    if (!isMediaValid()) {
      toastMessage({
        message: `Please upload a ${format} file to match the selected format.`,
      });
      return;
    }

    setLoading(true);

    const requests = hashtag.map(hash =>
      axios
        .get(`${dbConfig.url}/hashtags/${hash}`)
        .then(res => res.data.data.title),
    );

    let test;
    let filteredArray1;

    await Promise.all(requests)
      .then(titles => {
        test = titles;
        filteredArray1 = hashtagArray.filter(item => !test.includes(item));
      })
      .catch(error => {
        console.error(error);
      });

    if (selectedFileForStorage && step.url) {
      await axios
        .post(`${dbConfig?.url_storage}/deleteImage`, {file_name: step?.url})
        .then(res1 => {
          console.log(res1.data?.message, "updated successfully");
        })
        .catch(err => {
          console.log("delete file from storage err:", err);
        });
    }

    const date = new Date();
    const data2 = {
      activity: "step edited",
      dateTime: date,
      description: "a step is edited",
      answer: "Ans 1",
      machine: mid,
      module: parent !== "Alarm SOP" ? "Training" : parent,
      username: currentUser.username,
    };
    const data3 = {
      activity: "step edited",
      dateTime: date,
      description: "a step is edited",
      answer: "Ans 2",
      machine: mid,
      module: "Maintenance",
      username: currentUser.username,
    };

    let data = {
      title,
      desc,
      ans,
      url: selectedFileForStorage ? "" : step.url || "",
      type,
      format,
      sensor, // Contains _id values
      hashtag,
      qr_codes: qrcodes,
    };

    if (parent === "Maintenance") {
      const promises = hashtagArray?.map(hash => {
        return axios.post(`${dbConfig.url}/hashtags`, {
          maintenance_id: step.manual_id,
          title: hash,
          step_id: step._id,
          type: mType,
        });
      });

      if (selectedFileForStorage) {
        let fd = new FormData();
        fd.append("image", selectedFileForStorage);
        await axios
          .post(`${dbConfig?.url_storage}/upload`, fd, {
            onUploadProgress: progressEvent => {
              const {loaded, total} = progressEvent;
              const percentCompleted = Math.round((loaded * 100) / total);
              setUploadProgress(percentCompleted);
            },
          })
          .then(res => {
            data.url = res.data?.data;
          })
          .catch(err => {
            console.log(err);
            toastMessage({message: "Failed to upload media"});
            return;
          });
      }

      const responses = await Promise.all(promises);
      const newHashtagIds = responses?.map(response => response.data.data._id);
      data = {...data, hashtag: [...newHashtagIds]};

      try {
        await axios
          .put(`${dbConfig.url}/stepdata/${step._id}`, {
            ...step,
            ...data,
          })
          .then(response => {
            deletingHashTag();
            setRefreshCount(refreshCount + 1);
            // hashtagfunction();
            handleHastageData();
            toastMessageSuccess({
              message: `${data?.title} updated successfully!`,
            });
          });
      } catch (error) {
        console.log(error);
        toastMessage({message: "Failed to update step"});
      }

      handleClose();
    } else {
      // For Alarm SOP hashtags
      if (parent === "Alarm SOP") {
        const promises = hashtagArray?.map(hash => {
          return axios.post(`${dbConfig.url}/hashtags`, {
            maintenance_id: step.alarm_sop_id,
            title: hash,
            step_id: step._id,
          });
        });
        const responses = await Promise.all(promises);
        const newHashtagIds = responses?.map(
          response => response.data.data._id,
        );
        data = {...data, hashtag: [...newHashtagIds]};
      }

      if (selectedFileForStorage) {
        let fd = new FormData();
        fd.append("image", selectedFileForStorage);
        var restemp = await axios
          .post(`${dbConfig?.url_storage}/upload`, fd, {})
          .then(res1 => {
            return res1;
          })
          .catch(err => {
            toastMessage({message: err.message});
            return;
          });
        data.url = restemp?.data?.data;
      }

      const fullUrl =
        parent !== "Alarm SOP"
          ? `${dbConfig.url}/stepdata/${step._id}`
          : `${dbConfig.url}/alarmSopStepData/${step._id}`;
      axios
        .put(fullUrl, {
          ...step,
          ...data,
        })
        .then(() => {
          deletingHashTag();
          handleHastageData();
          edittrainigstepcfr(data2);
          toastMessageSuccess({
            message: `${data.title} updated successfully!`,
          });
          setRefreshCount(refreshCount + 1);
          handleClose();
        })
        .catch(err => {
          toastMessage({message: err.message});
        });
    }

    setLoading(false);
  };

  const handleFormat = newFormat => {
    setFormat(newFormat);
    if (newFormat !== step.format) {
      setFile(null);
      setSelectedFileForStorage("");
      setImageUrl("");
    }
  };

  const onCancel = () => {
    setTitle(step.title || "");
    setDesc(step.desc || "");
    setAns(step.ans || "");
    setType(step.type || "");
    setFormat(step.format || "");
    setFile(null);
    setSensor(step.sensor || []);
    setSensorValue("");
    setQrcodeValue("");
    setQrcodes(step.qr_codes || []);
    setHashtag("hashtag" in step ? step.hashtag : []);
    setImageUrl("");
    setSelectedFileForStorage("");
    setErrorMessage("");
    setErrorMessage2("");
    setHashtagArray([]);
    setHashtitle("");
    setFindBy("");
    setInputFindBy("");
    sethashdata("");
    sethashOBJ([]);
    handleClose();
  };

  return (
    <Dialog open={open} onClose={onCancel} maxWidth="lg" fullWidth>
      <DialogTitle
        style={
          currentMode === "Dark"
            ? {
                backgroundColor: "#212B36",
                color: "white",
                border: "0px solid white",
                borderRadius: "5px 5px 0 0",
              }
            : {border: "0px solid black", borderRadius: "5px 5px 0 0"}
        }>
        Edit Step
      </DialogTitle>
      <DialogContent
        style={
          currentMode === "Dark"
            ? {
                backgroundColor: "#212B36",
                color: "white",
                border: "0px solid white",
                borderRadius: "0 0 5px 5px ",
              }
            : {border: "0px solid black", borderRadius: "0 0 5px 5px"}
        }>
        <form
          onSubmit={handleSubmit}
          style={
            currentMode === "Dark"
              ? {backgroundColor: "#212B36", color: "white"}
              : {}
          }>
          <InputLabel style={{marginBottom: "10px"}}>Step Title *</InputLabel>
          <TextField
            onChange={handleTitleChange}
            onBlur={e => setTitle(title?.trim())}
            value={title}
            style={{marginBottom: "10px"}}
            variant="outlined"
            fullWidth
            required
            error={!!errorMessage}
            helperText={errorMessage}
          />

          <div
            style={{
              display: "flex",
              flexDirection: "row",
              alignItems: "flex-start",
              justifyContent: "space-between",
              width: "inherit",
            }}>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                width: "48%",
                alignSelf: "flex-start",
              }}>
              <InputLabel style={{marginBottom: "10px"}}>
                Sensor Values
              </InputLabel>
              <section className="flex">
                <Box sx={{width: "100%"}}>
                  <FormControl fullWidth>
                    <Select
                      labelId="demo-simple-select-label"
                      id="demo-simple-select"
                      value={sensorValue}
                      onChange={e => setSensorValue(e.target.value)}>
                      {sensorList?.map(mData => (
                        <MenuItem
                          key={mData._id}
                          value={mData._id}
                          sx={menuItemTheme}>
                          {mData.tag} {mData.value ? `(${mData.value})` : ""}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
                <IconButton
                  onClick={() =>
                    sensorValue
                      ? setSensor([...sensor, sensorValue])
                      : toastMessageWarning({message: "Missing sensor value"})
                  }>
                  <span className="font-normal text-sm" data-title="Add">
                    <AddIcon className="text-green-800 " />
                  </span>
                </IconButton>
              </section>

              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  flexWrap: "wrap",
                  marginTop: "5px",
                  columnGap: "1rem",
                }}>
                {sensor.length > 0 && (
                  <div style={{marginBottom: "20px"}}>
                    {sensor?.map((sensorId, idx) => {
                      const sensorData = sensorList.find(
                        s => s._id === sensorId,
                      );
                      return (
                        <div key={sensorId + idx}>
                          <span className="font-bold">{idx + 1}. </span>
                          {sensorData?.tag || "Unknown"}{" "}
                          {sensorData?.value ? `(${sensorData.value})` : ""}
                          <span
                            className="font-normal text-sm"
                            data-title="Delete">
                            <IconButton onClick={() => prePopData(sensorId)}>
                              <RemoveIcon className="text-red-700" />
                            </IconButton>
                          </span>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>

            <div
              style={{
                display: "flex",
                flexDirection: "column",
                minWidth: "48%",
                alignSelf: "flex-start",
              }}>
              <InputLabel style={{marginBottom: "10px"}}>Qr</InputLabel>
              <section style={{marginBottom: "10px"}} className="flex">
                <Box sx={{width: "100%"}}>
                  <TextField
                    onChange={e => setQrcodeValue(e.target.value)}
                    value={qrcodeValue}
                    required
                    onBlur={() => setQrcodeValue(qrcodeValue.trim())}
                    style={{marginBottom: "10px"}}
                    variant="outlined"
                    fullWidth
                  />
                </Box>
                <IconButton
                  className="bg-red-700"
                  onClick={() =>
                    qrcodeValue
                      ? setQrcodes([...qrcodes, qrcodeValue])
                      : toastMessageWarning({message: "Missing Qr value"})
                  }>
                  <AddIcon />
                </IconButton>
              </section>
              <div style={{marginBottom: "20px"}}>
                {qrcodes?.map((data, idx) => (
                  <div key={data + idx}>
                    <span className="font-bold">{idx + 1}. </span> {data}{" "}
                    <span>
                      <IconButton onClick={() => prePopDataQr(idx)}>
                        <RemoveIcon />
                      </IconButton>
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <InputLabel style={{marginBottom: "10px"}}>
            Step Description *
          </InputLabel>
          <TextField
            onChange={handleDescChange}
            onBlur={() => setDesc(desc?.trim())}
            style={{marginBottom: "10px"}}
            variant="outlined"
            value={desc}
            fullWidth
            multiline
            rows={2}
            error={!!errorMessage2}
            helperText={errorMessage2}
          />
          <InputLabel style={{marginBottom: "10px"}}>Answer</InputLabel>
          <TextField
            onChange={handleAnsChange}
            onBlur={() => setAns(ans?.trim())}
            style={{marginBottom: "10px"}}
            variant="outlined"
            value={ans}
            fullWidth
            multiline
            rows={2}
            error={!!errorMessage2}
            helperText={errorMessage2}
          />

          <div
            style={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              width: "inherit",
            }}>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                minWidth: "48%",
              }}>
              <InputLabel style={{marginBottom: "10px"}}>Format *</InputLabel>
              <FormControl
                style={{marginBottom: "10px"}}
                required
                fullWidth
                variant="outlined">
                <Select
                  value={format}
                  onChange={e => handleFormat(e.target.value)}>
                  <MenuItem value="image" sx={menuItemTheme}>
                    Image
                  </MenuItem>
                  <MenuItem value="video" sx={menuItemTheme}>
                    Video
                  </MenuItem>
                  <MenuItem value="audio" sx={menuItemTheme}>
                    Audio
                  </MenuItem>
                  <MenuItem value="text" sx={menuItemTheme}>
                    Text
                  </MenuItem>
                </Select>
              </FormControl>
            </div>

            <div
              style={{
                display: "flex",
                flexDirection: "column",
                minWidth: "48%",
              }}>
              <InputLabel style={{marginBottom: "10px"}}>Type *</InputLabel>
              <FormControl
                style={{marginBottom: "10px"}}
                required
                fullWidth
                variant="outlined">
                <Select
                  value={type}
                  onChange={e => setType(e.target.value)}
                  required>
                  <MenuItem value="info" sx={menuItemTheme}>
                    Info
                  </MenuItem>
                  <MenuItem value="camera" sx={menuItemTheme}>
                    Camera
                  </MenuItem>
                  <MenuItem value="critical" sx={menuItemTheme}>
                    Critical
                  </MenuItem>
                  <MenuItem value="normal" sx={menuItemTheme}>
                    Normal
                  </MenuItem>
                </Select>
              </FormControl>
            </div>
          </div>

          {format !== "text" && (
            <>
              <InputLabel style={{marginBottom: "10px"}}>Media</InputLabel>
              <DropzoneArea
                showFileNames
                onChange={loadedFiles => handleChange(loadedFiles)}
                onDelete={handleDelete}
                dropzoneText="Drag and Drop / Click to ADD Media"
                showAlerts={false}
                filesLimit={1}
                maxFileSize={50 * 1024 * 1024}
              />
              <div
                className="my-2"
                style={{display: "flex", justifyContent: "center"}}>
                <GetPreviewComponent
                  sourceUrl={
                    imageUrl.length > 0
                      ? imageUrl
                      : step.url && step.format === format
                      ? `${dbConfig?.url_storage}/${step.url}`
                      : ""
                  }
                  fileFormat={format}
                  previewImageStyle={{width: "450px"}}
                  previewVideoStyle={{width: "90%", marginTop: "20px"}}
                  previewAudioStyle={{
                    marginTop: "15%",
                    marginRight: "50px",
                    marginBottom: "20px",
                  }}
                />
              </div>
            </>
          )}
          {(parent === "Maintenance" || parent === "Alarm SOP") && (
            <>
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "space-between",
                }}>
                <div style={{width: "50%"}}>
                  <InputLabel style={{marginBottom: "10px"}}>
                    Hashtag
                  </InputLabel>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      width: "100%",
                    }}>
                    <Autocomplete
                      value={findBy}
                      onChange={(event, newValue) =>
                        setFindBy(newValue === null ? "" : newValue)
                      }
                      inputValue={inputFindBy}
                      onInputChange={(event, newInputValue) =>
                        handleChanges(newInputValue)
                      }
                      id="controllable-states-demo"
                      options={hashArray}
                      renderOption={(props, option) => (
                        <span
                          {...props}
                          style={
                            currentMode === "Dark"
                              ? {background: "#161C24", color: "white"}
                              : {background: currentColorLight}
                          }>
                          {option}
                        </span>
                      )}
                      sx={{width: "90%"}}
                      renderInput={params => (
                        <TextField
                          {...params}
                          inputRef={searchInput}
                          label="Search"
                        />
                      )}
                    />
                    <IconButton
                      onClick={handleButtonClickold}
                      style={{marginBottom: "10px"}}>
                      <span className="font-normal text-sm" data-title="Add">
                        <AddIcon className="text-green-800 " />
                      </span>
                    </IconButton>
                  </div>
                </div>

                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "flex-end",
                    justifyContent: "flex-start",
                    gap: "1rem",
                    marginBottom: "10px",
                    minWidth: "48%",
                  }}>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "flex-start",
                      justifyContent: "space-between",
                      minWidth: "70%",
                    }}>
                    <InputLabel style={{marginBottom: "10px"}}>
                      Add HashTag
                    </InputLabel>
                    <TextField
                      onChange={e => setHashtitle(e.target.value)}
                      style={{marginBottom: "10px"}}
                      variant="outlined"
                      value={hashtitle}
                      fullWidth
                    />
                  </div>
                  <div style={{marginBottom: "10px", width: "30%"}}>
                    <ButtonBasic
                      buttonTitle="ADD NEW"
                      color="secondary"
                      variant="contained"
                      onClick={handleButtonClick}
                    />
                  </div>
                </div>
              </div>

              <ul
                style={{
                  display: "flex",
                  flexDirection: "row",
                  columnGap: "0.5rem",
                  flexWrap: "wrap",
                }}>
                {hashtagArray?.map((item, index) => (
                  <li key={index}>
                    {item}
                    <IconButton onClick={() => removeHashtag(index)}>
                      <RemoveIcon style={{fontSize: "20px", color: "#f00"}} />
                    </IconButton>
                  </li>
                ))}
              </ul>
            </>
          )}
        </form>
      </DialogContent>
      <DialogActions
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "12px 36px",
          backgroundColor: currentMode === "Dark" ? "#212B36" : "#fff",
          color: currentMode === "Dark" ? "white" : "black",
        }}>
        <ButtonBasic
          buttonTitle="Cancel"
          width="30%"
          color="error"
          variant="contained"
          onClick={onCancel}
        />
        <SubmitButtons
          buttonTitle={
            uploadProgress > 0 ? `Uploading ${uploadProgress}%` : "Update Step"
          }
          width="30%"
          type="submit"
          color="primary"
          variant="contained"
          disabled={title === "" || desc === "" || !isMediaValid() || loading}
          onClick={handleSubmit}
        />
      </DialogActions>
    </Dialog>
  );
};

export default EditStep;
